<?php

namespace App\Services;

use App\Category;
use App\DailyMessageStats;
use App\Item;
use App\ItemLead;
use App\Jobs\LeadWhatsAppNotification;
use App\Jobs\LeadWhatsAppTwoNotification;
use App\Notification as notify_states;
use App\Services\CommonService;
use App\User;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class ItemLeadService
{
    public static function createUserListFiles($leadId)
    {
        // Simplified: Just send messages immediately to all relevant users
        self::sendLeadNotificationsImmediately($leadId);
    }

    public static function sendLeadNotificationsImmediately($leadId)
    {
        $itemLead = ItemLead::find($leadId);
        if (!$itemLead) {
            Log::error("ItemLead with ID $leadId not found");
            return;
        }

        // Get item details
        $itemQ = Item::find($itemLead->item_id);
        if (!$itemQ) {
            Log::error("Item with ID {$itemLead->item_id} not found");
            return;
        }

        // Get all users who should receive this lead notification
        $users = User::whereHas('items', function ($query) use ($itemQ) {
            $query->where('category_id', $itemQ->category_id)
                  ->where('city_id', $itemQ->city_id);
        })->get();

        Log::info("Found " . $users->count() . " users to notify for lead ID: $leadId");

        // Send notification to each user immediately
        foreach ($users as $user) {
            self::sendSingleNotification($itemLead, $user);
        }
    }

    private static function sendSingleNotification($itemLead, $user)
    {
        try {
            // Get category and item details
            $category = Category::find($itemLead->category_id);
            $categoryName = $category ? $category->category_name : '';
            $itemQ = Item::find($itemLead->item_id);
            $listing_city_name = $itemQ && $itemQ->city ? $itemQ->city->city_name : '';

            $leadData = [
                'item_lead_name' => $itemLead->item_lead_name,
                'item_lead_email' => $itemLead->item_lead_email,
                'item_lead_phone' => $itemLead->item_lead_phone,
                'item_lead_message' => $itemLead->item_lead_message,
                'event_date' => $itemLead->event_date,
                'category_name' => $categoryName,
                'budget' => $itemLead->budget,
            ];

            $mobile_ = CommonService::validateMobileNumberForWhatsapp($user->mobile);
            $mobile_ = ltrim($mobile_, '+'); // Remove + prefix for BotSailor API

            // Generate random password
            $randomPassword = random_int(100000, 999999);

            // Update user password
            $user->update([
                'password' => Hash::make($randomPassword),
            ]);

            // Send WhatsApp notification immediately (no delay)
            LeadWhatsAppTwoNotification::dispatch(188112, $mobile_, $randomPassword, $leadData, $listing_city_name);

            Log::info('Notification sent immediately:', [
                'user_id' => $user->id,
                'mobile' => $mobile_,
                'lead_id' => $itemLead->id,
                'random_password' => $randomPassword,
            ]);

        } catch (\Exception $e) {
            Log::error("Failed to send notification to user {$user->id}: " . $e->getMessage());
        }
    }

    public static function processMessageBatch()
    {
        // Simplified: This method is now just for backward compatibility
        // The actual sending happens immediately in createUserListFiles
        Log::info("processMessageBatch called - notifications are now sent immediately");
    }

    private static function formatPhoneNumber($phone)
    {
        // Remove any non-numeric characters
        $phone = preg_replace('/[^0-9]/', '', $phone);

        // Make sure it has the correct format for your database
        // For example, remove country code if needed
        if (strlen($phone) > 10 && substr($phone, 0, 2) == '91') {
            $phone = substr($phone, 2);
        }

        return $phone;
    }

    private static function getTodayMessageCount()
    {
        $today = now()->toDateString();
        $stats = DailyMessageStats::firstOrCreate(['date' => $today]);
        return $stats->total_messages_sent;
    }

    public static function updateMessageCount($category, $messagesProcessed)
    {
        $today = now()->toDateString();
        $stats = DailyMessageStats::firstOrCreate(['date' => $today]);

        // Directly update the category message count and the total messages sent
        $stats->update([
            "category_{$category}_messages" => DB::raw("category_{$category}_messages + {$messagesProcessed}"),
            'total_messages_sent' => DB::raw("total_messages_sent + {$messagesProcessed}")
        ]);
    }

    public static function getUserLoginStatsAndUpdateCategory()
    {
        $now = now();

        // Update users who logged in within 10 days
        User::where('logged_in_at', '>=', $now->subDays(10))->where('category', '!=', 'A')
            ->update(['category' => 'A']);

        // Update users who logged in between 11 and 30 days ago
        User::where('logged_in_at', '<', $now->subDays(10))
            ->where('logged_in_at', '>=', $now->subDays(30))
            ->where('category', '!=', 'A')
            ->update(['category' => 'B']);
    }
}
