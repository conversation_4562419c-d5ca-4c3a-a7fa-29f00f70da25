@extends('backend.admin.layouts.app')

@section('styles')
    <!-- Custom styles for this page -->
    <link href="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/vendor/datatables/buttons.dataTables.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.dataTables.min.css">
@endsection

@section('content')

    <div class="row justify-content-between">
        <div class="col-md-9">
            <h1 class="h3 mb-2 text-gray-800">{{ __('role_permission.item-leads.admin-index') }}</h1>
            <p class="mb-4">{{ __('role_permission.item-leads.admin-index-desc') }}</p>
        </div>
        <div class="col-md-3 text-md-right mb-3">
            <a href="{{ route('admin.leads.download') }}" class="btn btn-info btn-icon-split">
                <span class="icon text-white-50">
                  <i class="fas fa-plus"></i>
                </span>
                <span class="text">{{ __('Download') }}</span>
            </a>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row bg-white pt-4 pl-3 pr-3 pb-4">
        <div class="col-12">

            <div class="row">
                <div class="col-12">
                    
                    <div>
                        
                        <button 
                            onclick="$('.lead_id_checkboxes').prop('checked', true);" 
                            class="btn btn-info mb-2 btn-sm" 
                            id="select-all"
                        >
                            Select All
                        </button>
                        
                        <button 
                            onclick="$('.lead_id_checkboxes').prop('checked', false);" 
                            class="btn btn-info mb-2 btn-sm" 
                            id="deselect-all"
                        >
                            Deselect All
                        </button>
                        
                        <form 
                            id="delete_selected_form" 
                            action="{{route('admin.delete-selected')}}" 
                            method="post" 
                            class="d-inline-block"
                        >
                            @csrf
                            <button 
                                type="submit"
                                class="btn btn-danger mb-2 btn-sm"  
                                onclick="return confirm('Are you sure?');"
                            >
                                Delete Selected 
                            </button>
                        </form>

                        <button
                                id="mark-active-button"
                                class="btn btn-success mb-2 btn-sm"
                                onclick="confirm('Are you sure you want to activate the selected leads?') && markLeads(1)"
                        >
                            Mark Active
                        </button>

                        <button
                                id="mark-inactive-button"
                                class="btn btn-warning mb-2 btn-sm"
                                onclick="confirm('Are you sure you want to deactivate the selected leads?') && markLeads(0)"
                        >
                            Mark Inactive
                        </button>

                        <button
                                id="view-logs-button"
                                class="btn btn-info mb-2 btn-sm"
                                onclick="viewAllLogs()"
                        >
                            <i class="fas fa-file-alt"></i> View Logs
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead> 
                            <tr>
                                <th>{{ __('backend.city.id') }}</th>
                                <th>Lead</th>
                                <th>Details</th>
                                <th>{{ __('role_permission.item-leads.item-lead-subject') }}</th>
                                <th>{{ __('role_permission.item-leads.item-lead-message') }}</th>
                                <th>Budget</th>
                                <th>{{ __('role_permission.item-leads.item-lead-received-at') }}</th>
                                <th>{{ __('backend.shared.action') }}</th>
                            </tr>
                            </thead>
                            <tfoot>
                            <tr>
                                <th>{{ __('backend.city.id') }}</th>
                                <th>Lead</th>
                                <th>Details</th>
                                <th>{{ __('role_permission.item-leads.item-lead-subject') }}</th>
                                <th>{{ __('role_permission.item-leads.item-lead-message') }}</th>
                                <th>Budget</th>
                                <th>{{ __('role_permission.item-leads.item-lead-received-at') }}</th>
                                <th>{{ __('backend.shared.action') }}</th>
                            </tr>
                            </tfoot>
                            <tbody>
                                 
                            @foreach($all_item_leads as $all_item_leads_key => $item_lead)
                                <tr class="@if(!$item_lead->is_active) bg-secondary text-white @endif">
                                    <td>{{$item_lead->id }}</td>
                                    <td> 
                                            {{ $item_lead->item_lead_name }}
                                    </td>
                                    <td>
                                        {{ $item_lead->item_lead_email }}<br>
                                        {{ $item_lead->item_lead_phone }}<br>
                                    </td>
                                    <td>{{ $item_lead->item_lead_subject }}</td>
                                    <td>{{ $item_lead->item_lead_message }}</td>
                                    <td>{{ $item_lead->budget }}</td>
                                    <td>{{ $item_lead->created_at->diffForHumans() }}</td>
                                    <td>
                                        <input 
                                            form="delete_selected_form" 
                                            class="lead_id_checkboxes" 
                                            type="checkbox"
                                            name="item_lead_ids[]"
                                            value="{{$item_lead->id}}"
                                        >
                                        <a href="{{ route('admin.item-leads.edit', ['item_lead' => $item_lead]) }}" class="btn btn-primary btn-circle">
                                            <i class="fas fa-cog"></i>
                                        </a>
                                        <a href="#" class="btn btn-primary" id="send-lead-notification" onclick="sendLeadNotification({{$item_lead->id}})">
                                            Notify Users
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Modal -->
    <div class="modal fade" id="logsModal" tabindex="-1" role="dialog" aria-labelledby="logsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logsModalLabel">Lead Notification Logs</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <div id="logsContent" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="sr-only">Loading...</span>
                            </div>
                            <p class="mt-2">Loading logs...</p>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" onclick="refreshLogs()">Refresh</button>
                    <button type="button" class="btn btn-primary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('scripts')
    <!-- Page level plugins -->
    <script src="{{ asset('backend/vendor/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/dataTables.buttons.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/jszip.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/pdfmake.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/vfs_fonts.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/buttons.html5.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/buttons.print.min.js') }}"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    
    <script>
        // Call the dataTables jQuery plugin
        $(document).ready(function() {
            $('#dataTable').DataTable({
                responsive: true,
                "order": [[ 0, "desc" ]],
                "columnDefs": [
                    // {
                    //     "targets": [ 0 ],
                    //     "visible": false,
                    //     "searchable": false
                    // }
                ],
                dom: 'lBfrtip',
                buttons: [
                    {
                        extend: 'copy',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'csv',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'excel',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'pdf',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'print',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                ]
            });
        });

        function markLeads(status) {
            const selectedIds = getSelectedIds();
            if (selectedIds.length === 0) {
                alert('Please select at least one lead to proceed.');
                return;
            }

            $.ajax({
                url: "{{ route('admin.leads.mark-active') }}",
                type: 'POST',
                data: {
                    _token: "{{ csrf_token() }}",
                    item_lead_ids: selectedIds,
                    is_active: status
                },
                success: function(response) {
                    alert(`Selected leads marked as ${status ? 'active' : 'inactive'}.`);
                    location.reload(); // Optionally reload the page or update the UI
                },
                error: function() {
                    alert('Error updating lead status.');
                }
            });
        }

        // Utility function to get selected lead IDs
        function getSelectedIds() {
            const selectedIds = [];
            $('.lead_id_checkboxes:checked').each(function() {
                selectedIds.push($(this).val());
            });
            return selectedIds;
        }

        let currentLeadId = null;

        function sendLeadNotification(leadId) {
            currentLeadId = leadId;

            // Show a basic confirmation dialog
            if (confirm("Are you sure you want to notify the users?")) {
                // Show the logs modal immediately
                $('#logsModal').modal('show');
                loadLogs(leadId);

                // Send the AJAX request
                $.ajax({
                    url: "{{ route('admin.leads.send-lead-notification') }}",
                    type: 'POST',
                    data: {
                        _token: "{{ csrf_token() }}",
                        lead_id: leadId,
                    },
                    success: function(response, textStatus, xhr) {
                        // Check if the HTTP status is 200
                        if (xhr.status === 200) {
                            // Refresh logs after successful notification
                            setTimeout(() => {
                                loadLogs(leadId);
                            }, 2000);
                        } else {
                            alert('An error occurred, status code: ' + xhr.status);
                        }
                    },
                    error: function(xhr) {
                        // Handle any errors that occurred during the request
                        alert('An unexpected error occurred: ' + xhr.statusText);
                    }
                });
            }
        }

        function loadLogs(leadId) {
            // Update modal title
            $('#logsModalLabel').text(`Lead Notification Logs - Lead ID: ${leadId}`);

            $('#logsContent').html(`
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Loading logs...</p>
                </div>
            `);

            $.ajax({
                url: "{{ route('admin.leads.notification-logs') }}",
                type: 'GET',
                data: {
                    lead_id: leadId,
                },
                success: function(response) {
                    displayLogs(response.logs, response.timestamp);
                },
                error: function(xhr) {
                    $('#logsContent').html(`
                        <div class="alert alert-danger">
                            <strong>Error:</strong> Failed to load logs. ${xhr.statusText}
                        </div>
                    `);
                }
            });
        }

        function displayLogs(logs, timestamp) {
            let logsHtml = `<div class="mb-3"><strong>Last Updated:</strong> ${timestamp}</div>`;

            if (logs.length === 0) {
                logsHtml += '<div class="alert alert-info">No relevant logs found.</div>';
            } else {
                logsHtml += '<div style="font-family: monospace; font-size: 12px; line-height: 1.4;">';
                logs.forEach(log => {
                    // Color code different log levels
                    let logClass = '';
                    if (log.includes('[ERROR]') || log.includes('ERROR')) {
                        logClass = 'text-danger';
                    } else if (log.includes('[INFO]') || log.includes('INFO')) {
                        logClass = 'text-info';
                    } else if (log.includes('[WARNING]') || log.includes('WARNING')) {
                        logClass = 'text-warning';
                    }

                    logsHtml += `<div class="${logClass}" style="margin-bottom: 5px; word-break: break-all;">${escapeHtml(log)}</div>`;
                });
                logsHtml += '</div>';
            }

            $('#logsContent').html(logsHtml);
        }

        function refreshLogs() {
            if (currentLeadId) {
                loadLogs(currentLeadId);
            } else {
                viewAllLogs();
            }
        }

        function viewAllLogs() {
            currentLeadId = null; // Clear current lead ID to show all logs
            $('#logsModal').modal('show');
            loadAllLogs();
        }

        function loadAllLogs() {
            // Update modal title
            $('#logsModalLabel').text('All Lead Notification Logs');

            $('#logsContent').html(`
                <div class="text-center">
                    <div class="spinner-border" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2">Loading all logs...</p>
                </div>
            `);

            $.ajax({
                url: "{{ route('admin.leads.notification-logs') }}",
                type: 'GET',
                data: {
                    // No lead_id to get all logs
                },
                success: function(response) {
                    displayLogs(response.logs, response.timestamp);
                },
                error: function(xhr) {
                    $('#logsContent').html(`
                        <div class="alert alert-danger">
                            <strong>Error:</strong> Failed to load logs. ${xhr.statusText}
                        </div>
                    `);
                }
            });
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
@endsection
