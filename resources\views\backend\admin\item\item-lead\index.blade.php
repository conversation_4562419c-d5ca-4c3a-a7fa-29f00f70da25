@extends('backend.admin.layouts.app')

@section('styles')
    <!-- Custom styles for this page -->
    <link href="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.css') }}" rel="stylesheet">
    <link href="{{ asset('backend/vendor/datatables/buttons.dataTables.min.css') }}" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.datatables.net/responsive/2.2.9/css/responsive.dataTables.min.css">
@endsection

@section('content')

    <div class="row justify-content-between">
        <div class="col-md-9">
            <h1 class="h3 mb-2 text-gray-800">{{ __('role_permission.item-leads.admin-index') }}</h1>
            <p class="mb-4">{{ __('role_permission.item-leads.admin-index-desc') }}</p>
        </div>
        <div class="col-md-3 text-md-right mb-3">
            <a href="{{ route('admin.leads.download') }}" class="btn btn-info btn-icon-split">
                <span class="icon text-white-50">
                  <i class="fas fa-plus"></i>
                </span>
                <span class="text">{{ __('Download') }}</span>
            </a>
        </div>
    </div>

    <!-- Content Row -->
    <div class="row bg-white pt-4 pl-3 pr-3 pb-4">
        <div class="col-12">

            <div class="row">
                <div class="col-12">
                    
                    <div>
                        
                        <button 
                            onclick="$('.lead_id_checkboxes').prop('checked', true);" 
                            class="btn btn-info mb-2 btn-sm" 
                            id="select-all"
                        >
                            Select All
                        </button>
                        
                        <button 
                            onclick="$('.lead_id_checkboxes').prop('checked', false);" 
                            class="btn btn-info mb-2 btn-sm" 
                            id="deselect-all"
                        >
                            Deselect All
                        </button>
                        
                        <form 
                            id="delete_selected_form" 
                            action="{{route('admin.delete-selected')}}" 
                            method="post" 
                            class="d-inline-block"
                        >
                            @csrf
                            <button 
                                type="submit"
                                class="btn btn-danger mb-2 btn-sm"  
                                onclick="return confirm('Are you sure?');"
                            >
                                Delete Selected 
                            </button>
                        </form>

                        <button
                                id="mark-active-button"
                                class="btn btn-success mb-2 btn-sm"
                                onclick="confirm('Are you sure you want to activate the selected leads?') && markLeads(1)"
                        >
                            Mark Active
                        </button>

                        <button
                                id="mark-inactive-button"
                                class="btn btn-warning mb-2 btn-sm"
                                onclick="confirm('Are you sure you want to deactivate the selected leads?') && markLeads(0)"
                        >
                            Mark Inactive
                        </button>

                        <button
                                id="view-logs-button"
                                class="btn btn-info mb-2 btn-sm"
                                onclick="viewAllLogs()"
                        >
                            <i class="fas fa-file-alt"></i> View Message Queue
                        </button>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered" id="dataTable" width="100%" cellspacing="0">
                            <thead> 
                            <tr>
                                <th>{{ __('backend.city.id') }}</th>
                                <th>Lead</th>
                                <th>Details</th>
                                <th>{{ __('role_permission.item-leads.item-lead-subject') }}</th>
                                <th>{{ __('role_permission.item-leads.item-lead-message') }}</th>
                                <th>Budget</th>
                                <th>{{ __('role_permission.item-leads.item-lead-received-at') }}</th>
                                <th>{{ __('backend.shared.action') }}</th>
                            </tr>
                            </thead>
                            <tfoot>
                            <tr>
                                <th>{{ __('backend.city.id') }}</th>
                                <th>Lead</th>
                                <th>Details</th>
                                <th>{{ __('role_permission.item-leads.item-lead-subject') }}</th>
                                <th>{{ __('role_permission.item-leads.item-lead-message') }}</th>
                                <th>Budget</th>
                                <th>{{ __('role_permission.item-leads.item-lead-received-at') }}</th>
                                <th>{{ __('backend.shared.action') }}</th>
                            </tr>
                            </tfoot>
                            <tbody>
                                 
                            @foreach($all_item_leads as $all_item_leads_key => $item_lead)
                                <tr class="@if(!$item_lead->is_active) bg-secondary text-white @endif">
                                    <td>{{$item_lead->id }}</td>
                                    <td> 
                                            {{ $item_lead->item_lead_name }}
                                    </td>
                                    <td>
                                        {{ $item_lead->item_lead_email }}<br>
                                        {{ $item_lead->item_lead_phone }}<br>
                                    </td>
                                    <td>{{ $item_lead->item_lead_subject }}</td>
                                    <td>{{ $item_lead->item_lead_message }}</td>
                                    <td>{{ $item_lead->budget }}</td>
                                    <td>{{ $item_lead->created_at->diffForHumans() }}</td>
                                    <td>
                                        <input 
                                            form="delete_selected_form" 
                                            class="lead_id_checkboxes" 
                                            type="checkbox"
                                            name="item_lead_ids[]"
                                            value="{{$item_lead->id}}"
                                        >
                                        <a href="{{ route('admin.item-leads.edit', ['item_lead' => $item_lead]) }}" class="btn btn-primary btn-circle">
                                            <i class="fas fa-cog"></i>
                                        </a>
                                        <a href="#" class="btn btn-primary" id="send-lead-notification" onclick="sendLeadNotification({{$item_lead->id}})">
                                            Notify Users
                                        </a>
                                    </td>
                                </tr>
                            @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Logs Modal -->
    <div class="modal fade" id="logsModal" tabindex="-1" role="dialog" aria-labelledby="logsModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-xl" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="logsModalLabel">Lead Notification Logs</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Nav tabs -->
                    <ul class="nav nav-tabs" id="logsTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <a class="nav-link active" id="category-a-tab" data-toggle="tab" href="#category-a" role="tab" aria-controls="category-a" aria-selected="true">
                                <span class="badge badge-success">A</span> Category A
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="category-b-tab" data-toggle="tab" href="#category-b" role="tab" aria-controls="category-b" aria-selected="false">
                                <span class="badge badge-warning">B</span> Category B
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="category-c-tab" data-toggle="tab" href="#category-c" role="tab" aria-controls="category-c" aria-selected="false">
                                <span class="badge badge-secondary">C</span> Category C
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="user-logs-tab" data-toggle="tab" href="#user-logs" role="tab" aria-controls="user-logs" aria-selected="false">
                                <i class="fas fa-users"></i> User Logs
                            </a>
                        </li>
                        <li class="nav-item" role="presentation">
                            <a class="nav-link" id="system-logs-tab" data-toggle="tab" href="#system-logs" role="tab" aria-controls="system-logs" aria-selected="false">
                                <i class="fas fa-cog"></i> System Logs
                            </a>
                        </li>
                    </ul>

                    <!-- Tab panes -->
                    <div class="tab-content mt-3" id="logsTabContent">
                        <div class="tab-pane fade show active" id="category-a" role="tabpanel" aria-labelledby="category-a-tab">
                            <div id="categoryAContent" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <p class="text-muted">Loading Category A logs...</p>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="category-b" role="tabpanel" aria-labelledby="category-b-tab">
                            <div id="categoryBContent" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <p class="text-muted">Loading Category B logs...</p>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="category-c" role="tabpanel" aria-labelledby="category-c-tab">
                            <div id="categoryCContent" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <p class="text-muted">Loading Category C logs...</p>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="user-logs" role="tabpanel" aria-labelledby="user-logs-tab">
                            <div id="userLogsContent" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <p class="text-muted">Loading user activity logs...</p>
                            </div>
                        </div>
                        <div class="tab-pane fade" id="system-logs" role="tabpanel" aria-labelledby="system-logs-tab">
                            <div id="systemLogsContent" style="max-height: 400px; overflow-y: auto; background-color: #f8f9fa; padding: 15px; border-radius: 5px;">
                                <p class="text-muted">Loading system logs...</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-info btn-sm" onclick="refreshAllLogs()">
                        <i class="fas fa-sync-alt"></i> Refresh All
                    </button>
                    <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

@endsection

@section('scripts')
    <!-- Page level plugins -->
    <script src="{{ asset('backend/vendor/datatables/jquery.dataTables.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/dataTables.bootstrap4.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/dataTables.buttons.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/jszip.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/pdfmake.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/vfs_fonts.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/buttons.html5.min.js') }}"></script>
    <script src="{{ asset('backend/vendor/datatables/buttons.print.min.js') }}"></script>
    <script src="https://cdn.datatables.net/responsive/2.2.9/js/dataTables.responsive.min.js"></script>
    
    <script>
        // Call the dataTables jQuery plugin
        $(document).ready(function() {
            $('#dataTable').DataTable({
                responsive: true,
                "order": [[ 0, "desc" ]],
                "columnDefs": [
                    // {
                    //     "targets": [ 0 ],
                    //     "visible": false,
                    //     "searchable": false
                    // }
                ],
                dom: 'lBfrtip',
                buttons: [
                    {
                        extend: 'copy',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'csv',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'excel',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'pdf',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                    {
                        extend: 'print',
                        exportOptions: {
                            columns: [1,2,3,4,5,6,7]
                        }
                    },
                ]
            });
        });

        function markLeads(status) {
            const selectedIds = getSelectedIds();
            if (selectedIds.length === 0) {
                alert('Please select at least one lead to proceed.');
                return;
            }

            $.ajax({
                url: "{{ route('admin.leads.mark-active') }}",
                type: 'POST',
                data: {
                    _token: "{{ csrf_token() }}",
                    item_lead_ids: selectedIds,
                    is_active: status
                },
                success: function(response) {
                    alert(`Selected leads marked as ${status ? 'active' : 'inactive'}.`);
                    location.reload(); // Optionally reload the page or update the UI
                },
                error: function() {
                    alert('Error updating lead status.');
                }
            });
        }

        // Utility function to get selected lead IDs
        function getSelectedIds() {
            const selectedIds = [];
            $('.lead_id_checkboxes:checked').each(function() {
                selectedIds.push($(this).val());
            });
            return selectedIds;
        }

        let currentLeadId = null;

        function sendLeadNotification(leadId) {
            currentLeadId = leadId;

            // Show a basic confirmation dialog
            if (confirm("Are you sure you want to notify the users?")) {
                // Show the logs modal immediately
                $('#logsModal').modal('show');
                loadLogs(leadId);

                // Send the AJAX request
                $.ajax({
                    url: "{{ route('admin.leads.send-lead-notification') }}",
                    type: 'POST',
                    data: {
                        _token: "{{ csrf_token() }}",
                        lead_id: leadId,
                    },
                    success: function(response, textStatus, xhr) {
                        // Check if the HTTP status is 200
                        if (xhr.status === 200) {
                            // Show success message
                            alert(response.message || 'Notifications sent successfully!');
                            // Refresh logs after successful notification
                            setTimeout(() => {
                                loadLogs(leadId);
                            }, 1000);
                        } else {
                            alert('An error occurred, status code: ' + xhr.status);
                        }
                    },
                    error: function(xhr) {
                        // Handle any errors that occurred during the request
                        alert('An unexpected error occurred: ' + xhr.statusText);
                    }
                });
            }
        }

        function loadLogs(leadId) {
            currentLeadId = leadId;
            // Update modal title
            $('#logsModalLabel').text(`Lead Notification Logs - Lead ID: ${leadId}`);

            // Load all tabs
            loadCategoryLogs('A', leadId);
            loadCategoryLogs('B', leadId);
            loadCategoryLogs('C', leadId);
            loadUserLogs(leadId);
            loadSystemLogs(leadId);
        }

        function loadCategoryLogs(category, leadId = null) {
            const contentId = `category${category}Content`;
            $(`#${contentId}`).html(`
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading Category ${category} logs...</p>
                </div>
            `);

            $.ajax({
                url: "{{ route('admin.leads.notification-logs') }}",
                type: 'GET',
                data: {
                    lead_id: leadId,
                    category: category,
                    log_type: 'queue'
                },
                success: function(response) {
                    displayCategoryLogs(response.logs, response.timestamp, category, contentId);
                },
                error: function(xhr) {
                    $(`#${contentId}`).html(`
                        <div class="alert alert-danger">
                            <strong>Error:</strong> Failed to load Category ${category} logs. ${xhr.statusText}
                        </div>
                    `);
                }
            });
        }

        function loadUserLogs(leadId = null) {
            $('#userLogsContent').html(`
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading user logs...</p>
                </div>
            `);

            $.ajax({
                url: "{{ route('admin.leads.notification-logs') }}",
                type: 'GET',
                data: {
                    lead_id: leadId,
                    log_type: 'user'
                },
                success: function(response) {
                    displayUserLogs(response.users, response.timestamp);
                },
                error: function(xhr) {
                    $('#userLogsContent').html(`
                        <div class="alert alert-danger">
                            <strong>Error:</strong> Failed to load user logs. ${xhr.statusText}
                        </div>
                    `);
                }
            });
        }

        function loadSystemLogs(leadId = null) {
            $('#systemLogsContent').html(`
                <div class="text-center">
                    <div class="spinner-border spinner-border-sm" role="status">
                        <span class="sr-only">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">Loading system logs...</p>
                </div>
            `);

            $.ajax({
                url: "{{ route('admin.leads.notification-logs') }}",
                type: 'GET',
                data: {
                    lead_id: leadId,
                    log_type: 'system'
                },
                success: function(response) {
                    displaySystemLogs(response.logs, response.timestamp);
                },
                error: function(xhr) {
                    $('#systemLogsContent').html(`
                        <div class="alert alert-danger">
                            <strong>Error:</strong> Failed to load system logs. ${xhr.statusText}
                        </div>
                    `);
                }
            });
        }

        function displayCategoryLogs(logs, timestamp, category, contentId) {
            let logsHtml = `<div class="mb-3">
                <small class="text-muted"><strong>Last Updated:</strong> ${timestamp}</small>
                <span class="float-right">
                    <span class="badge badge-${getPriorityBadgeClass(category)}">${logs.length} entries</span>
                </span>
            </div>`;

            if (logs.length === 0) {
                logsHtml += `<div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No Category ${category} queue entries found.
                </div>`;
            } else {
                logsHtml += `<div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-file-alt"></i> Reading from: category_${category}_messages.txt
                    </small>
                </div>`;

                logs.forEach(log => {
                    if (log.type === 'marker') {
                        // Display markers differently
                        logsHtml += `<div class="alert alert-warning py-2 mb-2">
                            <i class="fas fa-flag"></i> <strong>Marker:</strong> ${escapeHtml(log.raw_line)}
                            <small class="text-muted float-right">Line ${log.line_number}</small>
                        </div>`;
                    } else if (log.data) {
                        // Display structured data
                        logsHtml += `<div class="card mb-2">
                            <div class="card-body py-2">
                                <div class="row">
                                    <div class="col-md-3">
                                        <strong>Lead ID:</strong> ${log.data.lead_id || 'N/A'}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>User ID:</strong> ${log.data.user_id || 'N/A'}
                                    </div>
                                    <div class="col-md-3">
                                        <strong>Priority:</strong> <span class="badge badge-${getPriorityBadgeClass(log.data.priority)}">${log.data.priority || 'N/A'}</span>
                                    </div>
                                    <div class="col-md-3">
                                        <small class="text-muted">Line ${log.line_number}</small>
                                    </div>
                                </div>
                            </div>
                        </div>`;
                    } else {
                        // Display raw line for other content
                        logsHtml += `<div class="text-muted mb-1" style="font-family: monospace; font-size: 11px; padding: 5px; background-color: #f8f9fa; border-radius: 3px;">
                            <strong>Line ${log.line_number}:</strong> ${escapeHtml(log.raw_line)}
                        </div>`;
                    }
                });
            }

            $(`#${contentId}`).html(logsHtml);
        }

        function displayUserLogs(users, timestamp) {
            let logsHtml = `<div class="mb-3">
                <small class="text-muted"><strong>Last Updated:</strong> ${timestamp}</small>
                <span class="float-right">
                    <span class="badge badge-info">${users.length} users</span>
                </span>
            </div>`;

            if (users.length === 0) {
                logsHtml += `<div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No users found for this lead.
                </div>`;
            } else {
                logsHtml += `<div class="table-responsive">
                    <table class="table table-sm table-striped">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Name</th>
                                <th>Mobile</th>
                                <th>Category</th>
                                <th>Last Updated</th>
                            </tr>
                        </thead>
                        <tbody>`;

                users.forEach(user => {
                    logsHtml += `<tr>
                        <td>${user.id}</td>
                        <td>${escapeHtml(user.name || 'N/A')}</td>
                        <td>${escapeHtml(user.mobile || 'N/A')}</td>
                        <td><span class="badge badge-${getPriorityBadgeClass(user.category)}">${user.category || 'N/A'}</span></td>
                        <td><small>${user.updated_at || 'N/A'}</small></td>
                    </tr>`;
                });

                logsHtml += `</tbody></table></div>`;
            }

            $('#userLogsContent').html(logsHtml);
        }

        function displaySystemLogs(logs, timestamp) {
            let logsHtml = `<div class="mb-3">
                <small class="text-muted"><strong>Last Updated:</strong> ${timestamp}</small>
                <span class="float-right">
                    <span class="badge badge-secondary">${logs.length} entries</span>
                </span>
            </div>`;

            if (logs.length === 0) {
                logsHtml += `<div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> No recent system logs found.
                </div>`;
            } else {
                logsHtml += `<div class="mb-2">
                    <small class="text-muted">
                        <i class="fas fa-file-alt"></i> Reading from: storage/logs/laravel.log
                    </small>
                </div>`;

                logs.forEach((log, index) => {
                    logsHtml += `<div class="mb-2 p-2" style="background-color: #f8f9fa; border-radius: 3px; font-family: monospace; font-size: 11px;">
                        ${escapeHtml(log)}
                    </div>`;
                });
            }

            $('#systemLogsContent').html(logsHtml);
        }

        function getPriorityBadgeClass(priority) {
            switch(priority) {
                case 'A': return 'success';
                case 'B': return 'warning';
                case 'C': return 'secondary';
                default: return 'light';
            }
        }

        function refreshAllLogs() {
            if (currentLeadId) {
                loadLogs(currentLeadId);
            } else {
                viewAllLogs();
            }
        }

        function viewAllLogs() {
            currentLeadId = null; // Clear current lead ID to show all logs
            $('#logsModal').modal('show');
            loadAllLogs();
        }

        function loadAllLogs() {
            // Update modal title
            $('#logsModalLabel').text('All Lead Notification Logs');

            // Load all tabs without lead filter
            loadCategoryLogs('A');
            loadCategoryLogs('B');
            loadCategoryLogs('C');
            loadUserLogs();
            loadSystemLogs();
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }
    </script>
@endsection
