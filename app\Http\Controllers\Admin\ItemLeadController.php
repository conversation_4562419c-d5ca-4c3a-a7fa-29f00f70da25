<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Item;
use App\ItemLead;
use App\Services\ItemLeadService;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\ValidationException;

class ItemLeadController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        
        $settings = app('site_global_settings');

        /**
         * Start SEO
         */
        SEOMeta::setTitle(__('role_permission.item-leads.seo.index', ['site_name' => empty($settings->setting_site_name) ? config('app.name', 'Laravel') : $settings->setting_site_name]));
        SEOMeta::setDescription('');
        SEOMeta::setCanonical(URL::current());
        SEOMeta::addKeyword($settings->setting_site_seo_home_keywords);
        /**
         * End SEO
         */

        $all_item_leads = ItemLead::orderBy('created_at', 'DESC')->get();

        return response()->view('backend.admin.item.item-lead.index', compact('all_item_leads'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $settings = app('site_global_settings');

        /**
         * Start SEO
         */
        SEOMeta::setTitle(__('role_permission.item-leads.seo.create', ['site_name' => empty($settings->setting_site_name) ? config('app.name', 'Laravel') : $settings->setting_site_name]));
        SEOMeta::setDescription('');
        SEOMeta::setCanonical(URL::current());
        SEOMeta::addKeyword($settings->setting_site_seo_home_keywords);
        /**
         * End SEO
         */

        $items = Item::orderBy('created_at', 'DESC')->get();

        return response()->view('backend.admin.item.item-lead.create',
            compact('items'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $request->validate([
            'item_slug' => 'required',
            'item_lead_name' => 'required|max:255',
            'item_lead_email' => 'required|email',
            'item_lead_phone' => 'nullable|numeric',
            'item_lead_subject' => 'nullable|max:255',
            'item_lead_message' => 'nullable|max:65535',
        ]);

        $item = Item::where('item_slug', $request->item_slug)->first();

        if($item)
        {
            $item_lead = new ItemLead(array(
                'item_id' => $item->id,
                'item_lead_name' => $request->item_lead_name,
                'item_lead_email' => $request->item_lead_email,
                'item_lead_phone' => $request->item_lead_phone,
                'item_lead_subject' => $request->item_lead_subject,
                'item_lead_message' => $request->item_lead_message,
            ));
            $item_lead->save();

            \Session::flash('flash_message', __('role_permission.item-leads.alert.item-lead-created'));
            \Session::flash('flash_type', 'success');

            return redirect()->route('admin.item-leads.edit', ['item_lead' => $item_lead]);
        }
        else
        {
            throw ValidationException::withMessages(['item_slug' => __('role_permission.item-leads.alert.listing-not-exist')]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param ItemLead $item_lead
     * @return RedirectResponse
     */
    public function show(ItemLead $item_lead)
    {
        return redirect()->route('admin.item-leads.edit', ['item_lead' => $item_lead]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param ItemLead $item_lead
     * @return Response
     */
    public function edit(ItemLead $item_lead)
    {
        $settings = app('site_global_settings');

        /**
         * Start SEO
         */
        SEOMeta::setTitle(__('role_permission.item-leads.seo.edit', ['site_name' => empty($settings->setting_site_name) ? config('app.name', 'Laravel') : $settings->setting_site_name]));
        SEOMeta::setDescription('');
        SEOMeta::setCanonical(URL::current());
        SEOMeta::addKeyword($settings->setting_site_seo_home_keywords);
        /**
         * End SEO
         */

        $items = Item::orderBy('created_at', 'DESC')->get();

        return response()->view('backend.admin.item.item-lead.edit',
            compact('items', 'item_lead'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param ItemLead $item_lead
     * @return RedirectResponse
     * @throws ValidationException
     */
    public function update(Request $request, ItemLead $item_lead)
    {
        $request->validate([
            'item_slug' => 'required',
            'item_lead_name' => 'required|max:255',
            'item_lead_email' => 'required|email',
            'item_lead_phone' => 'nullable|numeric',
            'item_lead_subject' => 'nullable|max:255',
            'item_lead_message' => 'nullable|max:255',
        ]);

        $item = Item::where('item_slug', $request->item_slug)->first();

        if($item)
        {
            $item_lead->item_id = $item->id;
            $item_lead->item_lead_name = $request->item_lead_name;
            $item_lead->item_lead_email = $request->item_lead_email;
            $item_lead->item_lead_phone = $request->item_lead_phone;
            $item_lead->item_lead_subject = $request->item_lead_subject;
            $item_lead->item_lead_message = $request->item_lead_message;
            $item_lead->save();

            \Session::flash('flash_message', __('role_permission.item-leads.alert.item-lead-updated'));
            \Session::flash('flash_type', 'success');

            return redirect()->route('admin.item-leads.edit', ['item_lead' => $item_lead]);
        }
        else
        {
            throw ValidationException::withMessages(['item_slug' => __('role_permission.item-leads.alert.listing-not-exist')]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ItemLead $item_lead
     * @return RedirectResponse
     */
    public function destroy(ItemLead $item_lead)
    {
        $item_lead->deleteItemLead();

        \Session::flash('flash_message', __('role_permission.item-leads.alert.item-lead-deleted'));
        \Session::flash('flash_type', 'success');

        return redirect()->route('admin.item-leads.index');
    }

    public function leadsDownload()
    {
        $table = ItemLead::all();
        $filename = "item-lead.csv";
        $handle = fopen($filename, 'w+');
        fputcsv($handle, array('Item ID', 'City ID', 'Event date', 'Name', 'Email', 'Phone', 'Message'));
    
        foreach($table as $row) {
            fputcsv($handle, array($row['item_id'], $row['city_id'], $row['event_date'], $row['item_lead_name'], $row['item_lead_email'], $row['item_lead_phone'], $row['item_lead_message']));
        }
    
        fclose($handle);
    
        $headers = array(
            'Content-Type' => 'text/csv',
        );
    
        return response()->download($filename, 'Itemleads.csv', $headers);
    }

    public function markActive(Request $request)
    {
        $itemLeadIds = $request->input('item_lead_ids');
        $isActive = $request->input('is_active');

        // Update the leads' active status
        ItemLead::whereIn('id', $itemLeadIds)->update(['is_active' => $isActive]);

        return redirect()->route('admin.item-leads.index')->with('success', 'Leads updated successfully.');
    }
    
    public function sendLeadNotification(Request $request)
    {
        // Validate the incoming request
        $request->validate([
            'lead_id' => 'required|integer|exists:item_leads,id',
        ]);

        $leadId = $request->input('lead_id');

        try {
            // Send notifications immediately to all relevant users
            ItemLeadService::createUserListFiles($leadId);
            ItemLeadService::processMessageBatch();

            // Return a success response
            return response()->json(['message' => 'Notification process initiated successfully.'], 200);
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error("Error sending lead notification: " . $e->getMessage());

            // Return an error response
            return response()->json(['message' => 'Failed to send notifications: ' . $e->getMessage()], 500);
        }
    }

    public function getLeadNotificationLogs(Request $request)
    {
        $leadId = $request->input('lead_id');
        $category = $request->input('category'); // A, B, C, or null for all
        $logType = $request->input('log_type', 'queue'); // queue, user, system

        if ($logType === 'user') {
            return $this->getUserLogs($leadId);
        } elseif ($logType === 'system') {
            return $this->getSystemLogs($leadId);
        }

        // Default to queue logs
        return $this->getQueueLogs($leadId, $category);
    }

    private function getQueueLogs($leadId = null, $category = null)
    {
        $priorities = $category ? [$category] : ['A', 'B', 'C'];
        $allLogs = [];

        foreach ($priorities as $priority) {
            $filePath = storage_path("app/category_{$priority}_messages.txt");

            if (file_exists($filePath)) {
                $fileContent = file_get_contents($filePath);
                $lines = explode("\n", $fileContent);

                foreach ($lines as $lineNumber => $line) {
                    if (empty(trim($line))) continue;

                    // Try to decode JSON to get structured data
                    $decoded = json_decode($line, true);

                    if ($decoded && is_array($decoded)) {
                        // If specific lead ID is provided, filter by that ID
                        if ($leadId && isset($decoded['lead_id']) && $decoded['lead_id'] != $leadId) {
                            continue;
                        }

                        $allLogs[] = [
                            'category' => $priority,
                            'line_number' => $lineNumber + 1,
                            'data' => $decoded,
                            'raw_line' => $line,
                            'file' => "category_{$priority}_messages.txt"
                        ];
                    } else {
                        // Handle non-JSON lines (like markers)
                        if ($line === '--- LEAD COMPLETED ---' || !empty(trim($line))) {
                            $allLogs[] = [
                                'category' => $priority,
                                'line_number' => $lineNumber + 1,
                                'data' => null,
                                'raw_line' => $line,
                                'file' => "category_{$priority}_messages.txt",
                                'type' => 'marker'
                            ];
                        }
                    }
                }
            }
        }

        // Sort by category and line number for better organization
        usort($allLogs, function($a, $b) {
            if ($a['category'] === $b['category']) {
                return $a['line_number'] - $b['line_number'];
            }
            return strcmp($a['category'], $b['category']);
        });

        return response()->json([
            'logs' => array_slice($allLogs, -100), // Return last 100 entries
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'lead_id' => $leadId,
            'category' => $category,
            'total_entries' => count($allLogs)
        ]);
    }

    private function getUserLogs($leadId = null)
    {
        // Get recent user activity related to lead notifications
        $query = \DB::table('users')
            ->select('users.id', 'users.name', 'users.mobile', 'users.category', 'users.updated_at')
            ->orderBy('users.updated_at', 'desc')
            ->limit(50);

        if ($leadId) {
            // If we have a lead ID, get users who should receive this lead
            $itemLead = ItemLead::find($leadId);
            if ($itemLead) {
                $itemQ = Item::find($itemLead->item_id);
                if ($itemQ) {
                    $query->join('items', 'users.id', '=', 'items.user_id')
                          ->where('items.category_id', $itemQ->category_id)
                          ->where('items.city_id', $itemQ->city_id);
                }
            }
        }

        $users = $query->get();

        return response()->json([
            'users' => $users,
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'lead_id' => $leadId
        ]);
    }

    private function getSystemLogs($leadId = null)
    {
        // Get Laravel logs related to lead notifications
        $logPath = storage_path('logs/laravel.log');
        $logs = [];

        if (file_exists($logPath)) {
            $logContent = file_get_contents($logPath);
            $lines = explode("\n", $logContent);

            // Get last 200 lines and filter for notification-related logs
            $recentLines = array_slice($lines, -200);

            foreach ($recentLines as $line) {
                if (stripos($line, 'notification') !== false ||
                    stripos($line, 'whatsapp') !== false ||
                    stripos($line, 'lead') !== false ||
                    ($leadId && stripos($line, "lead.*$leadId") !== false)) {
                    $logs[] = $line;
                }
            }
        }

        return response()->json([
            'logs' => array_slice($logs, -50), // Last 50 relevant log entries
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'lead_id' => $leadId
        ]);
    }
}
