<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Item;
use App\ItemLead;
use App\Services\ItemLeadService;
use Artesaos\SEOTools\Facades\SEOMeta;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\URL;
use Illuminate\Validation\ValidationException;

class ItemLeadController extends Controller
{
    /**
     * Display a listing of the resource.
     *
     * @return Response
     */
    public function index()
    {
        
        $settings = app('site_global_settings');

        /**
         * Start SEO
         */
        SEOMeta::setTitle(__('role_permission.item-leads.seo.index', ['site_name' => empty($settings->setting_site_name) ? config('app.name', 'Laravel') : $settings->setting_site_name]));
        SEOMeta::setDescription('');
        SEOMeta::setCanonical(URL::current());
        SEOMeta::addKeyword($settings->setting_site_seo_home_keywords);
        /**
         * End SEO
         */

        $all_item_leads = ItemLead::orderBy('created_at', 'DESC')->get();

        return response()->view('backend.admin.item.item-lead.index', compact('all_item_leads'));
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return Response
     */
    public function create()
    {
        $settings = app('site_global_settings');

        /**
         * Start SEO
         */
        SEOMeta::setTitle(__('role_permission.item-leads.seo.create', ['site_name' => empty($settings->setting_site_name) ? config('app.name', 'Laravel') : $settings->setting_site_name]));
        SEOMeta::setDescription('');
        SEOMeta::setCanonical(URL::current());
        SEOMeta::addKeyword($settings->setting_site_seo_home_keywords);
        /**
         * End SEO
         */

        $items = Item::orderBy('created_at', 'DESC')->get();

        return response()->view('backend.admin.item.item-lead.create',
            compact('items'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param Request $request
     * @return RedirectResponse
     * @throws ValidationException
     */
    public function store(Request $request)
    {
        $request->validate([
            'item_slug' => 'required',
            'item_lead_name' => 'required|max:255',
            'item_lead_email' => 'required|email',
            'item_lead_phone' => 'nullable|numeric',
            'item_lead_subject' => 'nullable|max:255',
            'item_lead_message' => 'nullable|max:65535',
        ]);

        $item = Item::where('item_slug', $request->item_slug)->first();

        if($item)
        {
            $item_lead = new ItemLead(array(
                'item_id' => $item->id,
                'item_lead_name' => $request->item_lead_name,
                'item_lead_email' => $request->item_lead_email,
                'item_lead_phone' => $request->item_lead_phone,
                'item_lead_subject' => $request->item_lead_subject,
                'item_lead_message' => $request->item_lead_message,
            ));
            $item_lead->save();

            \Session::flash('flash_message', __('role_permission.item-leads.alert.item-lead-created'));
            \Session::flash('flash_type', 'success');

            return redirect()->route('admin.item-leads.edit', ['item_lead' => $item_lead]);
        }
        else
        {
            throw ValidationException::withMessages(['item_slug' => __('role_permission.item-leads.alert.listing-not-exist')]);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param ItemLead $item_lead
     * @return RedirectResponse
     */
    public function show(ItemLead $item_lead)
    {
        return redirect()->route('admin.item-leads.edit', ['item_lead' => $item_lead]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param ItemLead $item_lead
     * @return Response
     */
    public function edit(ItemLead $item_lead)
    {
        $settings = app('site_global_settings');

        /**
         * Start SEO
         */
        SEOMeta::setTitle(__('role_permission.item-leads.seo.edit', ['site_name' => empty($settings->setting_site_name) ? config('app.name', 'Laravel') : $settings->setting_site_name]));
        SEOMeta::setDescription('');
        SEOMeta::setCanonical(URL::current());
        SEOMeta::addKeyword($settings->setting_site_seo_home_keywords);
        /**
         * End SEO
         */

        $items = Item::orderBy('created_at', 'DESC')->get();

        return response()->view('backend.admin.item.item-lead.edit',
            compact('items', 'item_lead'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param Request $request
     * @param ItemLead $item_lead
     * @return RedirectResponse
     * @throws ValidationException
     */
    public function update(Request $request, ItemLead $item_lead)
    {
        $request->validate([
            'item_slug' => 'required',
            'item_lead_name' => 'required|max:255',
            'item_lead_email' => 'required|email',
            'item_lead_phone' => 'nullable|numeric',
            'item_lead_subject' => 'nullable|max:255',
            'item_lead_message' => 'nullable|max:255',
        ]);

        $item = Item::where('item_slug', $request->item_slug)->first();

        if($item)
        {
            $item_lead->item_id = $item->id;
            $item_lead->item_lead_name = $request->item_lead_name;
            $item_lead->item_lead_email = $request->item_lead_email;
            $item_lead->item_lead_phone = $request->item_lead_phone;
            $item_lead->item_lead_subject = $request->item_lead_subject;
            $item_lead->item_lead_message = $request->item_lead_message;
            $item_lead->save();

            \Session::flash('flash_message', __('role_permission.item-leads.alert.item-lead-updated'));
            \Session::flash('flash_type', 'success');

            return redirect()->route('admin.item-leads.edit', ['item_lead' => $item_lead]);
        }
        else
        {
            throw ValidationException::withMessages(['item_slug' => __('role_permission.item-leads.alert.listing-not-exist')]);
        }
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param ItemLead $item_lead
     * @return RedirectResponse
     */
    public function destroy(ItemLead $item_lead)
    {
        $item_lead->deleteItemLead();

        \Session::flash('flash_message', __('role_permission.item-leads.alert.item-lead-deleted'));
        \Session::flash('flash_type', 'success');

        return redirect()->route('admin.item-leads.index');
    }

    public function leadsDownload()
    {
        $table = ItemLead::all();
        $filename = "item-lead.csv";
        $handle = fopen($filename, 'w+');
        fputcsv($handle, array('Item ID', 'City ID', 'Event date', 'Name', 'Email', 'Phone', 'Message'));
    
        foreach($table as $row) {
            fputcsv($handle, array($row['item_id'], $row['city_id'], $row['event_date'], $row['item_lead_name'], $row['item_lead_email'], $row['item_lead_phone'], $row['item_lead_message']));
        }
    
        fclose($handle);
    
        $headers = array(
            'Content-Type' => 'text/csv',
        );
    
        return response()->download($filename, 'Itemleads.csv', $headers);
    }

    public function markActive(Request $request)
    {
        $itemLeadIds = $request->input('item_lead_ids');
        $isActive = $request->input('is_active');

        // Update the leads' active status
        ItemLead::whereIn('id', $itemLeadIds)->update(['is_active' => $isActive]);

        return redirect()->route('admin.item-leads.index')->with('success', 'Leads updated successfully.');
    }
    
    public function sendLeadNotification(Request $request)
    {
        // Validate the incoming request
        $request->validate([
            'lead_id' => 'required|integer|exists:item_leads,id',
        ]);

        $leadId = $request->input('lead_id');

        try {
            // Dispatch the job to process the lead notifications in the background
            // ProcessLeadNotificationJob::dispatch($leadId);
        ItemLeadService::createUserListFiles($leadId);
        ItemLeadService::processMessageBatch();

            // Return a success response
            return response()->json(['message' => 'Notification process initiated successfully.'], 200);
        } catch (\Exception $e) {
            // Log the error for debugging
            Log::error("Error sending lead notification: " . $e->getMessage());

            // Return an error response
            return response()->json(['message' => 'Failed to initiate notification process.'], 500);
        }
    }

    public function getLeadNotificationLogs(Request $request)
    {
        $leadId = $request->input('lead_id');

        // Read the Laravel log file
        $logPath = storage_path('logs/laravel.log');
        $logs = [];

        if (file_exists($logPath)) {
            $logContent = file_get_contents($logPath);
            $logLines = explode("\n", $logContent);

            // Get the last 100 lines and filter for relevant logs
            $recentLines = array_slice($logLines, -100);

            foreach ($recentLines as $line) {
                if (empty(trim($line))) continue;

                // Filter for logs related to lead notifications, WhatsApp, or the specific lead ID
                $isRelevantLog = stripos($line, 'notification') !== false ||
                                stripos($line, 'whatsapp') !== false ||
                                stripos($line, 'lead') !== false ||
                                stripos($line, 'ItemLead') !== false ||
                                stripos($line, 'template') !== false ||
                                stripos($line, 'botsailor') !== false ||
                                stripos($line, 'message') !== false;

                // If specific lead ID is provided, also filter by that ID
                if ($leadId && $isRelevantLog) {
                    $isRelevantLog = stripos($line, "ID $leadId") !== false ||
                                    stripos($line, "lead_id: $leadId") !== false ||
                                    stripos($line, "leadId: $leadId") !== false;
                }

                if ($isRelevantLog) {
                    $logs[] = $line;
                }
            }
        }

        return response()->json([
            'logs' => array_slice($logs, -30), // Return last 30 relevant logs
            'timestamp' => now()->format('Y-m-d H:i:s'),
            'lead_id' => $leadId
        ]);
    }
}
