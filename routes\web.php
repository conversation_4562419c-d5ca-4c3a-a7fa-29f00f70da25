<?php

use App\Category_item;
use App\Item;
use App\Jobs\LeadWhatsAppInteractiveNotification;
use App\Jobs\LeadWhatsAppNotification;
use App\Jobs\LeadWhatsAppTwoNotification;
use App\Setting;
use App\ItemLead;
use App\Category;
use App\CustomField;
use App\Country;
use App\State;
use App\City;
use App\Product;
use App\Attribute;
use App\PhotoStore;
use App\Faq;
use App\SocialMedia;
use App\Testimonial;


use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('clear-cache', function () {
    \Artisan::call('cache:clear');
    \Artisan::call('config:clear');
    \Artisan::call('view:clear');
    \Artisan::call('route:clear');
    \Artisan::call('canvas:publish');
    return 'success';
});

use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Http;

// Route::post('/botsailor/webhook', function (Request $request) {
//     Log::info('Botsailor Webhook Received:', $request->all());

//     $data = $request->all();
//     if (isset($data['interactive']['button_reply'])) {
//         $phone = $data['from'];
//         $buttonId = $data['interactive']['button_reply']['id'];

//         if ($buttonId === 'yes_show_me_details') {
//             // Generate dynamic payment link
//             $leadPrice = 499; // Fetch from DB
//             $paymentUrl = "https://yourpaymentgateway.com/pay?amount=$leadPrice&phone=$phone";

//             // Send payment link using Botsailor API
//             Http::post('https://api.botsailor.com/send-message', [
//                 'phone' => $phone,
//                 'template_name' => 'lead_payment',
//                 'variables' => [
//                     'vendor_name' => 'Vendor Name', // Fetch from DB
//                     'lead_price' => $leadPrice,
//                     'payment_link' => $paymentUrl
//                 ]
//             ]);
//         }
//     }

//     return response()->json(['status' => 'success']);
// });

Route::get('/hash-password', function () {
    $password = '54321';
    $hashedPassword = Hash::make($password);

    $user = \App\User::findOrFail(1);
    $a = $user->update([
        'password' => Hash::make($password),
    ]);
});

Route::get('shedular-run', function () {

    if ((Setting::first())->setting_queue_work_in_shedular_run_is_enabled) {
        \Artisan::call('queue:work --once');
    }

    return 'success';
});



Route::get('cache', function () {
    Artisan::call('config:cache');
    echo "cache made";
});

/**
 * Start utils routes
 */
Route::get('test/email', function () {

    $send_mail = '<EMAIL>';

    dispatch(new App\Jobs\SendEmailJob($send_mail));

    dd('send mail successfully !!');
});
Route::get('/utils/link', 'UtilsController@makeSymlinks')->name('utils.link');
Route::get('/utils/cache', 'UtilsController@makeCache')->name('utils.cache');
// Route::get('/utils/logAndDeleteLatestDuplicateMobileAccounts', 'UtilsController@logAndDeleteLatestDuplicateMobileAccounts')->name('utils.logAndDeleteLatestDuplicateMobileAccounts');
/**
 * End utils routes
 */

/**
 * Start website routesss
 */

Route::get('/testStorage', 'transController@testStorage')->name('front.testStorage');
Route::get('/getimageUrl', 'transController@getImgUrl')->name('front.imageUrl');
Route::middleware(['demo', 'global_variables'])->group(function () {

    /**
     * Auth routes
     */
    Auth::routes(['verify' => true]);

    /**
     * Social login routes
     */
    Route::get('/localtext', 'transController@testLocal')->name('testLocal');
    // facebook
    Route::get('/auth/facebook', 'Auth\LoginController@redirectToFacebook')->name('auth.login.facebook');
    Route::get('/auth/facebook/callback', 'Auth\LoginController@handleFacebookCallback')->name('auth.login.facebook.callback');

    // google
    Route::get('/auth/google', 'Auth\LoginController@redirectToGoogle')->name('auth.login.google');
    Route::get('/auth/google/callback', 'Auth\LoginController@handleGoogleCallback')->name('auth.login.google.callback');

    // twitter
    Route::get('/auth/twitter', 'Auth\LoginController@redirectToTwitter')->name('auth.login.twitter');
    Route::get('/auth/twitter/callback', 'Auth\LoginController@handleTwitterCallback')->name('auth.login.twitter.callback');

    // linkedin
    Route::get('/auth/linkedin', 'Auth\LoginController@redirectToLinkedIn')->name('auth.login.linkedin');
    Route::get('/auth/linkedin/callback', 'Auth\LoginController@handleLinkedInCallback')->name('auth.login.linkedin.callback');

    // github
    Route::get('/auth/github', 'Auth\LoginController@redirectToGitHub')->name('auth.login.github');
    Route::get('/auth/github/callback', 'Auth\LoginController@handleGitHubCallback')->name('auth.login.github.callback');

    /**
     * Public routes
     */
    Route::get('/', 'PagesController@index')->name('page.home');
    Route::get('/trends', 'PagesController@trans')->name('page.trends');
    Route::get('/trends/{slug}', 'PagesController@trans')->name('page.trends');

    Route::get('/search', 'PagesController@search')->name('page.search');
    Route::post('lead/otp-confirm', 'AjaxLeadsController@confirmOtp');
    Route::post('custom-enquery-save', 'PagesController@customEnquerySave');
    Route::get('custom-enquery-update', 'PagesController@customEnqueryUpdate');

    Route::get('/about', 'PagesController@about')->name('page.about');
    Route::get('/contact', 'PagesController@contact')->name('page.contact');
    Route::post('/contact', 'PagesController@doContact')->name('page.contact.do');

    Route::get('/categories', 'PagesController@categories')->name('page.categories');
    Route::get('/category/{category_slug}', 'PagesController@category')->name('page.category');
    Route::get('/category/{category_slug}/state/{state_slug}', 'PagesController@categoryByState')->name('page.category.state');
    Route::get('/category/{category_slug}/state/{state_slug}/city/{city_slug}', 'PagesController@categoryByStateCity')->name('page.category.state.city');

    Route::get('/state/{state_slug}', 'PagesController@state')->name('page.state');
    Route::get('/state/{state_slug}/city/{city_slug}', 'PagesController@city')->name('page.city');

    Route::get('/listing/{item_slug}', 'PagesController@item')->name('page.item');
    Route::get('/listing/{item_slug}/product/{product_slug}', 'PagesController@product')->name('page.product');
    Route::get('forgot-password', 'Auth\ForgotPasswordController@forgot');
    Route::post('forgot-password', 'Auth\ForgotPasswordController@sendPin');
    Route::post('verify-pin', 'Auth\ForgotPasswordController@verifyPin');
    Route::post('reset-password', 'Auth\ForgotPasswordController@resetPassword');

    Route::middleware(['auth'])->group(function () {

        Route::post('/items/{item_slug}/email', 'PagesController@emailItem')->name('page.item.email');
        Route::post('/items/{item_slug}/save', 'PagesController@saveItem')->name('page.item.save');
        Route::post('/items/{item_slug}/unsave', 'PagesController@unSaveItem')->name('page.item.unsave');
    });

    Route::post('/items/{item_slug}/lead/store', 'PagesController@storeItemLead')->name('page.item.lead.store');

    Route::get('/pricing', 'PagesController@pricing')->name('page.pricing');
    Route::get('/terms-of-service', 'PagesController@termsOfService')->name('page.terms-of-service');
    Route::get('/privacy-policy', 'PagesController@privacyPolicy')->name('page.privacy-policy');

    /**
     * Blog routes
     */
    Route::group(['prefix' => 'blog'], function () {

        // Get all published posts
        Route::get('/', 'PagesController@blog')->name('page.blog');

        // Get posts for a given tag
        Route::get('/tag/{tag_slug}', 'PagesController@blogByTag')->name('page.blog.tag');

        // Get posts for a given topic
        Route::get('/topic/{topic_slug}', 'PagesController@blogByTopic')->name('page.blog.topic');

        // Find a single post
        Route::get('/{blog_slug}', 'PagesController@blogPost')
            ->middleware('Canvas\Http\Middleware\Session')
            ->name('page.blog.show');
    });

    Route::get('/locale/update/{user_prefer_language}', 'PagesController@updateLocale')->name('page.locale.update');
    Route::get('/country/update/{user_prefer_country_id}', 'PagesController@updateCountry')->name('page.country.update');

    /**
     * Start payment gateway webhooks
     */
    // PayPal IPN (Webhook) Route
    Route::post('/paypal/notify', 'User\PaypalController@notify')->name('user.paypal.notify');

    // Razorpay Webhook Route
    Route::post('/razorpay/notify', 'User\RazorpayController@notify')->name('user.razorpay.notify');

    // Stripe webhook route
    Route::post('/stripe/notify', 'User\StripeController@notify')->name('user.stripe.notify');

    Route::post('/webhook/botsailor', 'BotsailorWebhookController@handleWebhook');
    Route::post('/webhook/payment-success', 'PaymentWebhookController@paymentSuccess');

    // Wallet recharge webhook (handles both direct calls and text messages)
    Route::post('/webhook/wallet-recharge', 'WalletRechargeController@handleRecharge');
    Route::get('/wallet/recharge/payment', 'WalletRechargeController@showPayment')->name('wallet.recharge.payment');
    Route::post('/wallet/recharge/process', 'WalletRechargeController@processPayment')->name('wallet.recharge.process');
    Route::get('/wallet/recharge/success', 'WalletRechargeController@handlePaymentSuccess')->name('wallet.recharge.success');

    // Webhook payment routes
    Route::get('/webhook/lead/payment', 'WebhookPaymentController@showLeadPayment')->name('webhook.lead.payment');
    Route::get('/webhook/payment/success', 'WebhookPaymentController@handlePaymentSuccess')->name('webhook.payment.success');

//    https://clickawedding.com/webhook/botsailor?button_id=get_last_lead

    Route::get('/test-lead-whatsapp-old', function () {
        $vendor_phone = '+919844350994'; // Replace with a test number
        $vendor_name = 'Test Gourish';
        $lead_data = [
            'id' => 123,
            'name' => 'John Doe',
            'event_location' => 'Mumbai',
            'service_type' => 'Photography',
            'event_date' => '2025-04-15',
            'budget_min' => 10000,
            'budget_max' => 20000
        ];
        $lead_price = 500;
//        LeadWhatsAppNotification::dispatch('163275', $vendor_phone, "1234", $lead_data, 'test');

        LeadWhatsAppInteractiveNotification::dispatch($vendor_phone, $vendor_name, $lead_data, $lead_price);

        return 'Lead WhatsApp Interactive Notification Job Dispatched!';
    });

    Route::get('/test-lead-whatsapp', function () {
        $mobile_ = '+919844350994'; // Replace with a test number

        $model = ItemLead::find(3048);
        
        // $lead_data;
        $lead_data['item_lead_id'] = $model->id;
        $lead_data['item_lead_name'] = $model->item_lead_name;
        $lead_data['item_lead_email'] = $model->item_lead_email;
        $lead_data['item_lead_phone'] = $model->item_lead_phone;
        $lead_data['item_lead_message'] = $model->item_lead_message;
        $lead_data['category_name'] = 'asdasdas';
        $lead_data['budget'] = $model->budget;
        $lead_data['event_date'] = $model->event_date;

        LeadWhatsAppTwoNotification::dispatch('188112', $mobile_, '1234', $lead_data, 'test');

        return 'Lead WhatsApp template Notification Job Dispatched!';
    });

Route::get('/test-lead-whatsapp-template', function () {
    $service = new \App\Services\BotSailorService();

    $data = [
        'template_id' => '163275',
        'phone_number' => '919844350994',
        'template_quick_reply_button_values' => ["6H4K__ca6AjWL_K", "EXTERNAL_ECOMMERCE_CANCEL_ORDER"],
        'templateVariable-name-1' => 'NAME-VALUE',
    ];

    $response = $service->whatsapp_send_template_post_message($data);

    return response()->json($response);
});

    // Instamojo Webhook Route
    //Route::post('/instamojo/notify', 'User\InstamojoController@notify')->name('user.instamojo.notify');
    /**
     * End payment gateway webhooks
     */

    /**
     * Start site map routes
     */
    Route::get('/sitemap', 'SitemapController@index')->name('page.sitemap.index');
    Route::get('/sitemap/page', 'SitemapController@page')->name('page.sitemap.page');
    Route::get('/sitemap/category', 'SitemapController@category')->name('page.sitemap.category');
    Route::get('/sitemap/listing', 'SitemapController@listing')->name('page.sitemap.listing');
    Route::get('/sitemap/post', 'SitemapController@post')->name('page.sitemap.post');
    Route::get('/sitemap/tag', 'SitemapController@tag')->name('page.sitemap.tag');
    Route::get('/sitemap/topic', 'SitemapController@topic')->name('page.sitemap.topic');
    /**
     * End site map routes
     */


    /**
     * ajax routes serve frontend elements
     */
    Route::get('/ajax/cities/{state_id}', 'PagesController@jsonGetCitiesByState')->name('json.city');
    Route::get('/ajax/states/{country_id}', 'PagesController@jsonGetStatesByCountry')->name('json.state');

    Route::post('/ajax/item/image/delete/{item_id}', 'PagesController@jsonDeleteItemFeatureImage')->name('json.item.image.feature');
    Route::post('/ajax/item/gallery/delete/{item_image_gallery_id}', 'PagesController@jsonDeleteItemImageGallery')->name('json.item.image.gallery');
    Route::post('/ajax/item/review/gallery/delete/{review_image_gallery_id}', 'PagesController@jsonDeleteReviewImageGallery')->name('json.review.image.gallery');

    Route::post('/ajax/location/save/{lat}/{lng}', 'PagesController@ajaxLocationSave')->name('ajax.location.save');

    Route::post('/ajax/product/image/delete/{product_id}', 'PagesController@jsonDeleteProductFeatureImage')->name('json.product.image.feature');
    Route::post('/ajax/product/gallery/delete/{product_image_gallery_id}', 'PagesController@jsonDeleteProductImageGallery')->name('json.product.image.gallery');

    Route::post('/ajax/user/image/delete/{user_id}', 'PagesController@jsonDeleteUserProfileImage')->name('json.user.image.profile');

    Route::post('/ajax/setting/logo/delete', 'PagesController@jsonDeleteSettingLogoImage')->name('json.setting.logo');
    Route::post('/ajax/setting/favicon/delete', 'PagesController@jsonDeleteSettingFaviconImage')->name('json.setting.favicon');
    Route::get('ajax-lead-submit', 'AjaxLeadsController@ajaxLeadSubmit')->name('ajax-lead-submit');

    /**
     * Back-end admin routes
     */
    Route::group(['prefix' => 'admin', 'namespace' => 'Admin', 'middleware' => ['verified', 'auth', 'admin'], 'as' => 'admin.'], function () {

        Route::post('categories/delete-selected', function (Request $req) {
            Category::whereIn('id', $req->category_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('categories-delete-selected');


        Route::post('custom-field/delete-selected', function (Request $req) {
            CustomField::whereIn('id', $req->custom_field_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('custom-field.delete-selected');

        Route::post('countries/delete-selected', function (Request $req) {
            Country::whereIn('id', $req->country_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('countries.delete-selected');

        Route::post('states/delete-selected', function (Request $req) {
            State::whereIn('id', $req->state_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('states.delete-selected');

        Route::post('cities/delete-selected', function (Request $req) {
            City::whereIn('id', $req->city_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('cities.delete-selected');

        Route::post('products/delete-selected', function (Request $req) {
            Product::whereIn('id', $req->product_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('products.delete-selected');

        Route::post('attributes/delete-selected', function (Request $req) {
            Attribute::whereIn('id', $req->attribute_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('attributes.delete-selected');

        Route::post('photo-store/delete-selected', function (Request $req) {
            PhotoStore::whereIn('id', $req->photo_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('photo-store.delete-selected');


        Route::post('feature-photo-store/delete-selected', function (Request $req) {
            PhotoStore::whereIn('id', $req->photo_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('feature-photo-store.delete-selected');



        Route::post('faqs/delete-selected', function (Request $req) {
            Faq::whereIn('id', $req->ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('faqs.delete-selected');

        Route::post('social-medias/delete-selected', function (Request $req) {
            SocialMedia::whereIn('id', $req->ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('social-medias.delete-selected');


        Route::post('testimonials/delete-selected', function (Request $req) {
            Testimonial::whereIn('id', $req->ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('testimonials.delete-selected');



        Route::get('/dashboard', 'PagesController@index')->name('index');
        Route::resource('/countries', 'CountryController');
        Route::resource('/states', 'StateController');
        Route::resource('/cities', 'CityController');
        Route::resource('/categories', 'CategoryController');
        Route::resource('/custom-fields', 'CustomFieldController');
        Route::resource('/items', 'ItemController');

        Route::get('items-fakevote/{id}', 'ItemController@itemsFakevote')->name('items.fakevote');
        Route::post('fakevote-update', 'ItemController@itemsFakevoteUpdate')->name('fakeitem.update');


        Route::get('item-download', 'ItemController@itemsDownload')->name('items.download');

        // item slug update route
        Route::put('/items/{item}/slug/update', 'ItemController@updateItemSlug')->name('item.slug.update');

        Route::get('/photo-store/index', 'ProductController@uploadStorage')->name('admin-photo-store-index');
        Route::post('/photo-store/submit', 'ProductController@storephoto')->name('admin-photo-store-submit');
        Route::post('/photo-store/delete/{id}', 'ProductController@storephoto_delete')->name('admin-photo-store-delete');

        Route::get('/feature-photo-store/index', 'ProductController@feature_uploadStorage')->name('admin-feature-photo-store-index');
        Route::post('/feature-photo-store/submit', 'ProductController@feature_storephoto')->name('admin-feature-photo-store-submit');
        Route::post('/feature-photo-store/delete/{id}', 'ProductController@feature_storephoto_delete')->name('admin-feature-photo-store-delete');




        // item section & collection routes for admin
        Route::get('/items/{item}/sections/index', 'ItemController@indexItemSections')->name('items.sections.index');
        Route::post('/items/{item}/sections/store', 'ItemController@storeItemSection')->name('items.sections.store');
        Route::get('/items/{item}/sections/{item_section}/edit', 'ItemController@editItemSection')->name('items.sections.edit');
        Route::put('/items/{item}/sections/{item_section}/update', 'ItemController@updateItemSection')->name('items.sections.update');
        Route::delete('/items/{item}/sections/{item_section}/destroy', 'ItemController@destroyItemSection')->name('items.sections.destroy');

        Route::put('/items/{item}/sections/{item_section}/rank-up', 'ItemController@rankUpItemSection')->name('items.sections.rank.up');
        Route::put('/items/{item}/sections/{item_section}/rank-down', 'ItemController@rankDownItemSection')->name('items.sections.rank.down');

        Route::post('/items/{item}/sections/{item_section}/collections/store', 'ItemController@storeItemSectionCollections')->name('items.sections.collections.store');
        Route::put('/items/{item}/sections/{item_section}/collections/{item_section_collection}/rank-up', 'ItemController@rankUpItemSectionCollection')->name('items.sections.collections.rank.up');
        Route::put('/items/{item}/sections/{item_section}/collections/{item_section_collection}/rank-down', 'ItemController@rankDownItemSectionCollection')->name('items.sections.collections.rank.down');
        Route::delete('/items/{item}/sections/{item_section}/collections/{item_section_collection}/destroy', 'ItemController@destroyItemSectionCollection')->name('items.sections.collections.destroy');

        // item claims routes for admin
        Route::resource('/item-claims', 'ItemClaimController');
        Route::post('/item-claims/download/{item_claim}', 'ItemClaimController@downloadItemClaimDoc')->name('item-claims.download.do');
        Route::post('/item-claims/{item_claim}/approve', 'ItemClaimController@approveItemClaim')->name('item-claims.approve.do');
        Route::post('/item-claims/{item_claim}/disapprove', 'ItemClaimController@disapproveItemClaim')->name('item-claims.disapprove.do');

        Route::put('/items/{item}/category/update', 'ItemController@updateItemCategory')->name('item.category.update');

        Route::get('/items/saved/index', 'ItemController@savedItems')->name('items.saved');
        Route::post('/items/{item_slug}/unsave', 'ItemController@unSaveItem')->name('items.unsave');

        Route::put('/items/{item}/approve', 'ItemController@approveItem')->name('items.approve');
        Route::put('/items/{item}/disapprove', 'ItemController@disApproveItem')->name('items.disapprove');
        Route::put('/items/{item}/suspend', 'ItemController@suspendItem')->name('items.suspend');

        Route::post('/items/bulk/approve', 'ItemController@bulkApproveItem')->name('items.bulk.approve');
        Route::post('/items/bulk/disapprove', 'ItemController@bulkDisapproveItem')->name('items.bulk.disapprove');
        Route::post('/items/bulk/suspend', 'ItemController@bulkSuspendItem')->name('items.bulk.suspend');
        Route::post('/items/bulk/delete', 'ItemController@bulkDeleteItem')->name('items.bulk.delete');

        // item reviews routes
        Route::get('/items/{item_slug}/reviews/create', 'ItemController@itemReviewsCreate')->name('items.reviews.create');
        Route::post('/items/{item_slug}/reviews/store', 'ItemController@itemReviewsStore')->name('items.reviews.store');
        Route::get('/items/{item_slug}/reviews/{review}/edit', 'ItemController@itemReviewsEdit')->name('items.reviews.edit');
        Route::put('/items/{item_slug}/reviews/update/{review}', 'ItemController@itemReviewsUpdate')->name('items.reviews.update');
        Route::delete('/items/{item_slug}/reviews/destroy/{review}', 'ItemController@itemReviewsDestroy')->name('items.reviews.destroy');

        // item reviews management admin routes
        Route::get('/items/reviews/index', 'ItemController@itemReviewsIndex')->name('items.reviews.index');
        Route::get('/items/reviews/{review_id}', 'ItemController@itemReviewsShow')->name('items.reviews.show');
        Route::put('/items/reviews/update/{review_id}/approve', 'ItemController@itemReviewsApprove')->name('items.reviews.approve');
        Route::put('/items/reviews/update/{review_id}/disapprove', 'ItemController@itemReviewsDisapprove')->name('items.reviews.disapprove');
        Route::delete('/items/reviews/destroy/{review_id}', 'ItemController@itemReviewsDelete')->name('items.reviews.delete');

        // message routes
        Route::resource('/messages', 'MessageController');

        // plan routes
        Route::resource('/plans', 'PlanController');

        // subscription routes
        Route::resource('/subscriptions', 'SubscriptionController');
        Route::get('subscriptions-download', 'SubscriptionController@subscriptionListDownload')->name('subscriptions.download');

        // product routes
        Route::resource('/products', 'ProductController');
        Route::put('/products/{product}/attribute/update', 'ProductController@updateProductAttribute')->name('product.attribute.update');

        Route::put('/products/{product}/approve', 'ProductController@approveProduct')->name('product.approve');
        Route::put('/products/{product}/disapprove', 'ProductController@disapproveProduct')->name('product.disapprove');
        Route::put('/products/{product}/suspend', 'ProductController@suspendProduct')->name('product.suspend');

        Route::put('/products/{product}/feature/{product_feature}/rank-up', 'ProductController@rankUpProductFeature')->name('product.feature.up');
        Route::put('/products/{product}/feature/{product_feature}/rank-down', 'ProductController@rankDownProductFeature')->name('product.feature.down');
        Route::delete('/products/{product}/feature/{product_feature}/destroy', 'ProductController@destroyProductFeature')->name('product.feature.destroy');

        // product attribute routes
        Route::resource('/attributes', 'AttributeController');

        Route::resource('/users', 'UserController');
        Route::get('/users/password/{user}/edit', 'UserController@editUserPassword')->name('users.password.edit');
        Route::post('/users/password/{user}', 'UserController@updateUserPassword')->name('users.password.update');

        Route::get('/login-as/{user}', 'UserController@loginAsVendor')->name('login-as');

        Route::put('/users/{user}/update-wallet', 'UserController@updateWallet')->name('users.update-wallet');

        Route::put('/users/{user}/suspend', 'UserController@suspendUser')->name('users.suspend');
        Route::put('/users/{user}/unsuspend', 'UserController@unsuspendUser')->name('users.unsuspend');

        Route::post('/users/bulk/verify', 'UserController@bulkVerifyUser')->name('users.bulk.verify');
        Route::post('/users/bulk/suspend', 'UserController@bulkSuspendUser')->name('users.bulk.suspend');
        Route::post('/users/bulk/unsuspend', 'UserController@bulkUnsuspendUser')->name('users.bulk.unsuspend');
        Route::post('/users/bulk/delete', 'UserController@bulkDeleteUser')->name('users.bulk.delete');
        Route::post('/users/bulk/update-category', 'UserController@bulkUpdateCategory')->name('users.bulk.update-category');

        Route::get('/profile', 'UserController@editProfile')->name('users.profile.edit');
        Route::post('/profile', 'UserController@updateProfile')->name('users.profile.update');
        Route::get('/profile/password', 'UserController@editProfilePassword')->name('users.profile.password.edit');
        Route::post('/profile/password', 'UserController@updateProfilePassword')->name('users.profile.password.update');

        Route::resource('/testimonials', 'TestimonialController');
        Route::resource('/faqs', 'FaqController');
        Route::resource('/social-medias', 'SocialMediaController');

        // setting general
        Route::get('/settings/general', 'SettingController@editGeneralSetting')->name('settings.general.edit');
        Route::post('/settings/general', 'SettingController@updateGeneralSetting')->name('settings.general.update');

        // setting home page
        Route::get('/settings/home', 'SettingController@editHomePageSetting')->name('settings.page.home.edit');
        Route::post('/settings/home', 'SettingController@updateHomePageSetting')->name('settings.page.home.update');

        // setting about page
        Route::get('/settings/about', 'SettingController@editAboutPageSetting')->name('settings.page.about.edit');
        Route::post('/settings/about', 'SettingController@updateAboutPageSetting')->name('settings.page.about.update');

        // setting terms-of-service page
        Route::get('/settings/terms-of-service', 'SettingController@editTermsOfServicePageSetting')->name('settings.page.terms-service.edit');
        Route::post('/settings/terms-of-service', 'SettingController@updateTermsOfServicePageSetting')->name('settings.page.terms-service.update');

        // setting privacy-policy page
        Route::get('/settings/privacy-policy', 'SettingController@editPrivacyPolicyPageSetting')->name('settings.page.privacy-policy.edit');
        Route::post('/settings/privacy-policy', 'SettingController@updatePrivacyPolicyPageSetting')->name('settings.page.privacy-policy.update');

        // setting google recaptcha v2
        Route::get('/settings/recaptcha', 'SettingController@editRecaptchaSetting')->name('settings.recaptcha.edit');
        Route::post('/settings/recaptcha', 'SettingController@updateRecaptchaSetting')->name('settings.recaptcha.update');

        // setting site map
        Route::get('/settings/sitemap', 'SettingController@editSitemapSetting')->name('settings.sitemap.edit');
        Route::post('/settings/sitemap', 'SettingController@updateSitemapSetting')->name('settings.sitemap.update');

        // setting website cache
        Route::get('/settings/cache', 'SettingController@editCacheSetting')->name('settings.cache.edit');
        Route::post('/settings/cache', 'SettingController@updateCacheSetting')->name('settings.cache.update');
        Route::delete('/settings/cache/destroy', 'SettingController@deleteCacheSetting')->name('settings.cache.destroy');

        // setting product
        //        Route::get('/settings/product', 'ProductController@editProductSetting')->name('product.setting.edit');
        //        Route::post('/settings/product', 'ProductController@updateProductSetting')->name('product.setting.update');

        Route::get('/settings/product', 'SettingController@editProductSetting')->name('settings.product.edit');
        Route::post('/settings/product', 'SettingController@updateProductSetting')->name('settings.product.update');

        // setting item
        Route::get('/settings/item', 'SettingController@editItemSetting')->name('settings.item.edit');
        Route::post('/settings/item', 'SettingController@updateItemSetting')->name('settings.item.update');

        /**
         * Start payment gateway settings
         */
        // bank transfer setting
        Route::get('/settings/payment/bank-transfer', 'SettingController@indexBankTransferPaymentSetting')->name('settings.payment.bank-transfer.index');
        Route::get('/settings/payment/bank-transfer/create', 'SettingController@createBankTransferPaymentSetting')->name('settings.payment.bank-transfer.create');
        Route::post('/settings/payment/bank-transfer/store', 'SettingController@storeBankTransferPaymentSetting')->name('settings.payment.bank-transfer.store');
        Route::get('/settings/payment/bank-transfer/{setting_bank_transfer}/edit', 'SettingController@editBankTransferPaymentSetting')->name('settings.payment.bank-transfer.edit');
        Route::put('/settings/payment/bank-transfer/{setting_bank_transfer}', 'SettingController@updateBankTransferPaymentSetting')->name('settings.payment.bank-transfer.update');
        Route::delete('/settings/payment/bank-transfer/{setting_bank_transfer}', 'SettingController@destroyBankTransferPaymentSetting')->name('settings.payment.bank-transfer.destroy');

        Route::get('/settings/payment/bank-transfer/pending', 'SettingController@indexPendingBankTransferPayment')->name('settings.payment.bank-transfer.pending.index');
        Route::get('/settings/payment/bank-transfer/pending/{invoice}', 'SettingController@showPendingBankTransferPayment')->name('settings.payment.bank-transfer.pending.show');
        Route::post('/settings/payment/bank-transfer/pending/{invoice}/approve', 'SettingController@approveBankTransferPayment')->name('settings.payment.bank-transfer.pending.approve');
        Route::post('/settings/payment/bank-transfer/pending/{invoice}/reject', 'SettingController@rejectBankTransferPayment')->name('settings.payment.bank-transfer.pending.reject');

        // paypal setting
        Route::get('/settings/payment/paypal', 'SettingController@editPayPalPaymentSetting')->name('settings.payment.paypal.edit');
        Route::post('/settings/payment/paypal', 'SettingController@updatePayPalPaymentSetting')->name('settings.payment.paypal.update');

        // razorpay setting
        Route::get('/settings/payment/razorpay', 'SettingController@editRazorpayPaymentSetting')->name('settings.payment.razorpay.edit');
        Route::post('/settings/payment/razorpay', 'SettingController@updateRazorpayPaymentSetting')->name('settings.payment.razorpay.update');

        // stripe setting
        Route::get('/settings/payment/stripe', 'SettingController@editStripePaymentSetting')->name('settings.payment.stripe.edit');
        Route::post('/settings/payment/stripe', 'SettingController@updateStripePaymentSetting')->name('settings.payment.stripe.update');

        // payumoney setting
        Route::get('/settings/payment/payumoney', 'SettingController@editPayumoneyPaymentSetting')->name('settings.payment.payumoney.edit');
        Route::post('/settings/payment/payumoney', 'SettingController@updatePayumoneyPaymentSetting')->name('settings.payment.payumoney.update');


        // Leads Price Setting
        Route::get('/settings/leads-price/edit', 'LeadPriceController@leadPriceEdit')->name('settings.leadsprice.edit');
        Route::post('/settings/leads-price/update', 'LeadPriceController@leadPriceUpdate')->name('settings.leadsprice.update');

        // Whatsapp settings for facebook official
        Route::get('/settings/official-whatsapp/index', 'WhatsAppOfficialController@index')->name('settings.whatsapp-official.index');
        Route::post('/settings/official-whatsapp/index', 'WhatsAppOfficialController@update')->name('settings.whatsapp-official.update');

        // Follow up routes
        Route::get('/settings/follow-up/index', 'FollowUpController@index')->name('settings.follow-up.view');
        Route::post('/settings/follow-up/update', 'FollowUpController@update')->name('settings.follow-up.update');
        Route::get('/settings/follow-up/increase', 'FollowUpController@increase')->name('settings.follow-up.increase');
        Route::get('/settings/follow-up/decrease/{id}', 'FollowUpController@decrease')->name('settings.follow-up.decrease');

        // WhatsApp settings
        Route::get('/settings/whatsapp/index', 'WhatsAppController@index')->name('settings.whatsapp.index');
        Route::post('/settings/whatsapp/index', 'WhatsAppController@update')->name('settings.whatsapp.update');


        // Telegram Setting
        Route::get('/settings/telegram/index', 'TelegramController@index')->name('settings.telegram.index');
        Route::get('/settings/telegram/create', 'TelegramController@create')->name('settings.telegram.create');
        Route::get('/settings/telegram/{id}/edit', 'TelegramController@edit')->name('settings.telegram.edit');
        Route::post('/settings/telegram/store', 'TelegramController@store')->name('settings.telegram.store');
        Route::post('/settings/telegram/update/{id}', 'TelegramController@update')->name('settings.telegram.update');
        Route::get('/settings/telegram/{id}/delete', 'TelegramController@destroy')->name('settings.telegram.delete');

        // Telegram & Twilio Setting
        Route::get('/settings/twilio/edit', 'TwilioController@edit')->name('settings.twilio.edit');
        Route::post('/settings/twilio/update', 'TwilioController@update')->name('settings.twilio.update');
        Route::get('/settings/localtext/edit', 'TwilioController@localTxtedit')->name('settings.local.edit');
        Route::post('/settings/localtext/update', 'TwilioController@localTxtupdate')->name('settings.local.update');

        // Notification settings
        Route::get('/settings/notification', 'NotificationController@index')->name('settings.notification.view');
        Route::post('/settings/notification', 'NotificationController@update')->name('settings.notification.update');


        /**
         * End payment gateway settings
         */

        Route::get('/comments', 'CommentController@index')->name('comments.index');
        Route::put('/comments/{comment}/approve', 'CommentController@approve')->name('comments.approve');
        Route::put('/comments/{comment}/disapprove', 'CommentController@disapprove')->name('comments.disapprove');
        Route::delete('/comments/{comment}/delete', 'CommentController@destroy')->name('comments.destroy');

        // advertisement management
        Route::resource('/advertisements', 'AdvertisementController');

        // social login management
        Route::resource('/social-logins', 'SocialLoginController');

        // language translation management
        Route::get('/lang/sync', 'LangController@syncIndex')->name('lang.sync.index');
        Route::post('/lang/sync/do', 'LangController@syncDo')->name('lang.sync.do');
        Route::post('/lang/sync/restore', 'LangController@syncRestore')->name('lang.sync.restore');

        // customization routes
        Route::get('/customization/color', 'CustomizationController@colorEdit')->name('customization.color.edit');
        Route::post('/customization/color', 'CustomizationController@colorUpdate')->name('customization.color.update');
        Route::post('/customization/color/restore', 'CustomizationController@colorRestore')->name('customization.color.restore');

        Route::get('/customization/header', 'CustomizationController@headerEdit')->name('customization.header.edit');
        Route::post('/customization/header', 'CustomizationController@headerUpdate')->name('customization.header.update');


        /**
         * Start busslk importerss routesdd
         */
        Route::get('/importer/csv/upload', 'ImporterController@showUpload')->name('importer.csv.upload.show');
        Route::post('/importer/csv/upload', 'ImporterController@processUpload')->name('importer.csv.upload.process');

        Route::get('/importer/csv/data', 'ImporterController@indexCsvData')->name('importer.csv.upload.data.index');
        Route::get('/importer/csv/data/{import_csv_data}/edit', 'ImporterController@editCsvData')->name('importer.csv.upload.data.edit');
        Route::post('/importer/csv/data/{import_csv_data}/parse-ajax', 'ImporterController@ajaxParseCsvData')->name('importer.csv.upload.data.parse');
        Route::post('/importer/csv/data/{import_csv_data}/parse/progress-ajax', 'ImporterController@ajaxParseProgressCsvData')->name('importer.csv.upload.data.parse.progress');
        Route::delete('/importer/csv/data/{import_csv_data}/destroy', 'ImporterController@destroyCsvData')->name('importer.csv.upload.data.destroy');

        Route::get('/importer/item/data', 'ImporterController@indexItemData')->name('importer.item.data.index');
        Route::get('/importer/item/data/{import_item_data}/edit', 'ImporterController@editItemData')->name('importer.item.data.edit');
        Route::put('/importer/item/data/{import_item_data}/update', 'ImporterController@updateItemData')->name('importer.item.data.update');
        Route::delete('/importer/item/data/{import_item_data}/destroy', 'ImporterController@destroyItemData')->name('importer.item.data.destroy');
        Route::post('/importer/item/data/{import_item_data}/destroy-ajax', 'ImporterController@ajaxDestroyItemData')->name('importer.item.data.destroy-ajax');
        Route::post('/importer/item/data/{import_item_data}/import', 'ImporterController@importItemData')->name('importer.item.data.import');
        Route::post('/importer/item/data/{import_item_data}/import-ajax', 'ImporterController@ajaxImportItemData')->name('importer.item.data.import-ajax');
        Route::post('/importer/item/data/import-ajax-batch', 'ImporterController@ajaxImportItemDataBatch')->name('importer.item.data.import-ajax-batch');


        Route::get('/importer/vendor-type-upload', 'ImporterController@vendorTypeView')->name('importer.vendorType.upload.show');
        Route::post('/importer/vendor-type/submit', 'ImporterController@vendorTypeSubmit')->name('importer.vendorType.upload.process');
        Route::get('/importer/vendor-type-show', 'ImporterController@vendorTypeIndex')->name('importer.vendorType.data.show');
        Route::delete('/importer/vendor-type/data/{import_csv_data}/destroy', 'ImporterController@destroyVendorTypeData')->name('importer.vendorType.upload.data.destroy');
        Route::get('/importer/vendor-type-show/{id}/edit', 'ImporterController@vendorTypeEdit')->name('importer.vendorType.data.edit');
        Route::post('/importer/vendor-type-show/update/{id}', 'ImporterController@vendorTypeUpdate')->name('importer.vendorType.data.update');
        /**
         * End bulk importer routes
         */

        /**
         * Start theme routes
         */
        Route::get('/themes', 'ThemeController@index')->name('themes.index');
        Route::get('/themes/create', 'ThemeController@createTheme')->name('themes.create');
        Route::post('/themes/store', 'ThemeController@storeTheme')->name('themes.store');
        Route::post('/themes/{theme}/active', 'ThemeController@activeTheme')->name('themes.active');
        Route::delete('/themes/{theme}/destroy', 'ThemeController@destroyTheme')->name('themes.destroy');

        Route::get('/themes/{theme}/customization/color/edit', 'ThemeController@editThemeColor')->name('themes.customization.color.edit');
        Route::post('/themes/{theme}/customization/color/update', 'ThemeController@updateThemeColor')->name('themes.customization.color.update');
        Route::post('/themes/{theme}/customization/color/restore', 'ThemeController@restoreThemeColor')->name('themes.customization.color.restore');

        Route::get('/themes/{theme}/customization/header/edit', 'ThemeController@editThemeHeader')->name('themes.customization.header.edit');
        Route::post('/themes/{theme}/customization/header/update', 'ThemeController@updateThemeHeader')->name('themes.customization.header.update');
        Route::post('/themes/{theme}/customization/header/restore', 'ThemeController@restoreThemeHeader')->name('themes.customization.header.restore');
        /**
         * End theme routes
         */

        // item leads routes
        Route::post('item-leads/delete-selected', function (Request $req) {
            ItemLead::whereIn('id', $req->item_lead_ids)->delete();
            return redirect()->back()->with(['message' => "Deleted Successfully!"]);;
        })->name('delete-selected');

        Route::resource('/item-leads', 'ItemLeadController');

        Route::post('leads/mark-active', 'ItemLeadController@markActive')->name('leads.mark-active');

        Route::get('leads-download', 'ItemLeadController@leadsDownload')->name('leads.download');
        Route::post('send-lead-notification', 'ItemLeadController@sendLeadNotification')->name('leads.send-lead-notification');
        Route::get('lead-notification-logs', 'ItemLeadController@getLeadNotificationLogs')->name('leads.notification-logs');

    });

    /**
     * Back-end user routes
     */
    Route::group(['prefix' => 'user', 'namespace' => 'User', 'middleware' => ['verified', 'auth', 'user'], 'as' => 'user.'], function () {

        Route::get('/dashboard', 'PagesController@index')->name('index');
        Route::resource('/items', 'ItemController');

        Route::post('/items/bulk/delete', 'ItemController@bulkDeleteItem')->name('items.bulk.delete');

        // item slug update route
        Route::put('/items/{item}/slug/update', 'ItemController@updateItemSlug')->name('item.slug.update');

        // item section & collection routes for admin
        Route::get('/items/{item}/sections/index', 'ItemController@indexItemSections')->name('items.sections.index');
        Route::post('/items/{item}/sections/store', 'ItemController@storeItemSection')->name('items.sections.store');
        Route::get('/items/{item}/sections/{item_section}/edit', 'ItemController@editItemSection')->name('items.sections.edit');
        Route::put('/items/{item}/sections/{item_section}/update', 'ItemController@updateItemSection')->name('items.sections.update');
        Route::delete('/items/{item}/sections/{item_section}/destroy', 'ItemController@destroyItemSection')->name('items.sections.destroy');

        Route::put('/items/{item}/sections/{item_section}/rank-up', 'ItemController@rankUpItemSection')->name('items.sections.rank.up');
        Route::put('/items/{item}/sections/{item_section}/rank-down', 'ItemController@rankDownItemSection')->name('items.sections.rank.down');

        Route::post('/items/{item}/sections/{item_section}/collections/store', 'ItemController@storeItemSectionCollections')->name('items.sections.collections.store');
        Route::put('/items/{item}/sections/{item_section}/collections/{item_section_collection}/rank-up', 'ItemController@rankUpItemSectionCollection')->name('items.sections.collections.rank.up');
        Route::put('/items/{item}/sections/{item_section}/collections/{item_section_collection}/rank-down', 'ItemController@rankDownItemSectionCollection')->name('items.sections.collections.rank.down');
        Route::delete('/items/{item}/sections/{item_section}/collections/{item_section_collection}/destroy', 'ItemController@destroyItemSectionCollection')->name('items.sections.collections.destroy');

        // item claims routes for user
        Route::resource('/item-claims', 'ItemClaimController');
        Route::post('/item-claims/download/{item_claim}', 'ItemClaimController@downloadItemClaimDoc')->name('item-claims.download.do');

        Route::put('/items/{item}/category/update', 'ItemController@updateItemCategory')->name('item.category.update');

        Route::get('/items/saved/index', 'ItemController@savedItems')->name('items.saved');
        Route::post('/items/{item_slug}/unsave', 'ItemController@unSaveItem')->name('items.unsave');

        // item reviews routes
        Route::get('/items/{item_slug}/reviews/create', 'ItemController@itemReviewsCreate')->name('items.reviews.create');
        Route::post('/items/{item_slug}/reviews/store', 'ItemController@itemReviewsStore')->name('items.reviews.store');
        Route::get('/items/{item_slug}/reviews/{review}/edit', 'ItemController@itemReviewsEdit')->name('items.reviews.edit');
        Route::put('/items/{item_slug}/reviews/update/{review}', 'ItemController@itemReviewsUpdate')->name('items.reviews.update');
        Route::delete('/items/{item_slug}/reviews/destroy/{review}', 'ItemController@itemReviewsDestroy')->name('items.reviews.destroy');

        // user manage reviews route
        Route::get('/items/reviews/index', 'ItemController@itemReviewsIndex')->name('items.reviews.index');

        // message routes
        Route::resource('/messages', 'MessageController');

        // subscription routes
        Route::resource('/subscriptions', 'SubscriptionController');

        // product routes
        Route::resource('/products', 'ProductController');
        Route::put('/products/{product}/attribute/update', 'ProductController@updateProductAttribute')->name('product.attribute.update');
        Route::put('/products/{product}/feature/{product_feature}/rank-up', 'ProductController@rankUpProductFeature')->name('product.feature.up');
        Route::put('/products/{product}/feature/{product_feature}/rank-down', 'ProductController@rankDownProductFeature')->name('product.feature.down');
        Route::delete('/products/{product}/feature/{product_feature}/destroy', 'ProductController@destroyProductFeature')->name('product.feature.destroy');





        // product attribute routes
        Route::resource('/attributes', 'AttributeController');

        Route::get('/comments', 'CommentController@index')->name('comments.index');

        /**
         * Start Payment Gateway Routes
         */

        // Bank Transfer manual payment gateway
        Route::post('/banktransfer/checkout/plan/{plan_id}/subscription/{subscription_id}', 'BankTransferController@doCheckout')->name('banktransfer.checkout.do');
        Route::post('/banktransfer/recurring/cancel', 'BankTransferController@cancelRecurring')->name('banktransfer.recurring.cancel');

        // PayPal gateway
        Route::get('/paypal/checkout/plan/{plan_id}/subscription/{subscription_id}', 'PaypalController@doCheckout')->name('paypal.checkout.do');
        Route::get('/paypal/checkout/success/plan/{plan_id}/subscription/{subscription_id}', 'PaypalController@showCheckoutSuccess')->name('paypal.checkout.success');
        Route::get('/paypal/checkout/cancel', 'PaypalController@showCheckoutCancel')->name('paypal.checkout.cancel');
        Route::post('/paypal/recurring/cancel', 'PaypalController@cancelRecurring')->name('paypal.recurring.cancel');

        // indian payment gateway - instamojo
        //Route::get('/instamojo/checkout/plan/{plan_id}/subscription/{subscription_id}', 'InstamojoController@doCheckout')->name('instamojo.checkout.do');
        //Route::get('/instamojo/checkout/success/plan/{plan_id}/subscription/{subscription_id}', 'InstamojoController@showCheckoutSuccess')->name('instamojo.checkout.success');

        // indian payment gateway - Razorpay
        Route::post('/razorpay/checkout/plan/{plan_id}/subscription/{subscription_id}', 'RazorpayController@doCheckout')->name('razorpay.checkout.do');
        Route::get('/razorpay/checkout/success/plan/{plan_id}/subscription/{subscription_id}/invoice/{invoice_num}', 'RazorpayController@showCheckoutSuccess')->name('razorpay.checkout.success');
        Route::post('/razorpay/checkout/cancel', 'RazorpayController@cancelCheckout')->name('razorpay.checkout.cancel');
        Route::post('/razorpay/recurring/cancel', 'RazorpayController@cancelRecurring')->name('razorpay.recurring.cancel');

        // Stripe payment gateway
        Route::post('/stripe/checkout/plan/{plan_id}/subscription/{subscription_id}', 'StripeController@doCheckout')->name('stripe.checkout.do');
        Route::get('/stripe/checkout/success/plan/{plan_id}/subscription/{subscription_id}', 'StripeController@showCheckoutSuccess')->name('stripe.checkout.success');
        Route::get('/stripe/checkout/cancel', 'StripeController@cancelCheckout')->name('stripe.checkout.cancel');
        Route::post('/stripe/recurring/cancel', 'StripeController@cancelRecurring')->name('stripe.recurring.cancel');

        // Payumoney payment gateway
        Route::post('/payumoney/checkout/plan/{plan_id}/subscription/{subscription_id}', 'PayumoneyController@doCheckout')->name('payumoney.checkout.do');
        Route::post('/payumoney/checkout/success', 'PayumoneyController@showCheckoutSuccess')->name('payumoney.checkout.success');
        Route::post('/payumoney/checkout/cancel', 'PayumoneyController@cancelCheckout')->name('payumoney.checkout.cancel');
        Route::post('/payumoney/recurring/cancel', 'PayumoneyController@cancelRecurring')->name('payumoney.recurring.cancel');
        /**
         * End Payment Gateway Routes
         */

        // user profile routes
        Route::get('/profile', 'UserController@editProfile')->name('profile.edit');
        Route::post('/profile', 'UserController@updateProfile')->name('profile.update');
        Route::get('/profile/password', 'UserController@editProfilePassword')->name('profile.password.edit');
        Route::post('/profile/password', 'UserController@updateProfilePassword')->name('profile.password.update');

        // item leads routes
        Route::resource('/item-leads', 'ItemLeadController');
        Route::get('each-lead-payment-page/{id}', 'ItemLeadController@eachLeadPaymentView');
        Route::get('each-lead-payment-razor/{id}', 'ItemLeadController@eachLeadPaymentRazor')->name('eachLead.razor');
        Route::get('buy-lead-by-wallet/{id}', 'ItemLeadController@payWithWallet')->name('buy-lead-by-wallet');
        Route::get('/razorpay/item/checkout/success/{item_id}', 'ItemLeadController@showCheckoutSuccess')->name('item.razorpay.checkout.success');

        //==================================== wallet routes 
        Route::get('/mywallet', 'WalletController@index')->name('wallet.show');
        Route::get('/lead-subscription/{lead_id}', 'WalletController@confirmPayement')->name('wallet.confirmPayment');
        Route::post('/confirmedPayment', 'WalletController@confirmedPayment')->name('wallet.confirmedPayment');
        Route::get('/wallet-deposit', 'WalletController@deposit')->name('wallet.deposit');
        Route::post('/wallet-deposit-payment-razor', 'WalletController@depositPaymentRazor')->name('walletPayment.razor');
        Route::get('/wallet-deposit-payment-success', 'WalletController@successPayment')->name('wallet.razorpay.checkout.success');
        Route::post('/wallet-deposit-payment-cancel', 'WalletController@cancelPayment')->name('walletPayment.cancel');
    });
});
/**
 * End website routes
 */


// saving device token
Route::post('store-token', function (Request $req) {

    if (auth()->check()) {

        $user = auth()->user();
        $role = $user->role;

        // if the user is general user saving the token against that user
        if ($role->id == 3) {
            $user->device_token = $req->token;
            $user->save();
        }

        return response()->json(['Token successfully stored.']);
    }
})->name('store.token');
